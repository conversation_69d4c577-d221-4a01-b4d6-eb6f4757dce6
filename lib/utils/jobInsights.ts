/**
 * Job insights utility functions for aggregating and analyzing job data
 * Used by the HiringInsightsCard component to transform raw job data into market intelligence
 */

import { 
  JobListing, 
  CompanyActivity, 
  JobInsightsData, 
  InsightsConfig, 
  JobInsightsInput,
  InsightsCalculationResult,
  InsightsDisplayMode 
} from '@/types/jobInsights';
import { roleDisplayNames, JobRole } from '@/lib/roles';

// Default configuration for insights calculation
const DEFAULT_CONFIG: InsightsConfig = {
  recentDaysThreshold: 14,
  maxTopCompanies: 3,
  largeDatasetThreshold: 1000
};

/**
 * Groups jobs by company and calculates activity metrics
 * @param jobs Array of job listings to analyze
 * @param logoCache Optional company logo cache
 * @param config Configuration options
 * @returns Array of CompanyActivity objects sorted by job count (descending)
 */
export function aggregateJobsByCompany(
  jobs: JobListing[],
  logoCache: Record<string, string | null> = {},
  config: Partial<InsightsConfig> = {}
): CompanyActivity[] {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  
  // Calculate the cutoff date for "recent" jobs
  const recentCutoffDate = new Date();
  recentCutoffDate.setDate(recentCutoffDate.getDate() - finalConfig.recentDaysThreshold);
  
  // Group jobs by company
  const companyJobMap = new Map<string, JobListing[]>();
  
  jobs.forEach(job => {
    const companyName = job.company_name;
    if (!companyJobMap.has(companyName)) {
      companyJobMap.set(companyName, []);
    }
    companyJobMap.get(companyName)!.push(job);
  });
  
  // Calculate activity metrics for each company
  const companyActivities: CompanyActivity[] = [];
  
  companyJobMap.forEach((companyJobs, companyName) => {
    // Count total jobs for this company
    const jobCount = companyJobs.length;
    
    // Count recent jobs (posted within the threshold)
    const recentJobCount = companyJobs.filter(job => {
      if (!job.date_posted) return false;
      
      try {
        const jobDate = new Date(job.date_posted);
        return jobDate >= recentCutoffDate;
      } catch {
        // If date parsing fails, don't count as recent
        return false;
      }
    }).length;
    
    // Get logo URL from cache
    const logoUrl = logoCache[companyName] || null;
    
    companyActivities.push({
      name: companyName,
      jobCount,
      recentJobCount,
      logoUrl
    });
  });
  
  // Sort by total job count (descending), then by recent job count (descending)
  companyActivities.sort((a, b) => {
    if (a.jobCount !== b.jobCount) {
      return b.jobCount - a.jobCount;
    }
    return b.recentJobCount - a.recentJobCount;
  });
  
  return companyActivities;
}

/**
 * Calculates the total number of recent jobs across all companies
 * @param jobs Array of job listings
 * @param recentDaysThreshold Number of days to consider as "recent"
 * @returns Number of jobs posted within the recent threshold
 */
export function calculateRecentJobsCount(
  jobs: JobListing[],
  recentDaysThreshold: number = DEFAULT_CONFIG.recentDaysThreshold
): number {
  const recentCutoffDate = new Date();
  recentCutoffDate.setDate(recentCutoffDate.getDate() - recentDaysThreshold);
  
  return jobs.filter(job => {
    if (!job.date_posted) return false;
    
    try {
      const jobDate = new Date(job.date_posted);
      return jobDate >= recentCutoffDate;
    } catch {
      return false;
    }
  }).length;
}

/**
 * Formats role value for display
 * @param role Role value (could be '*' for all roles or enum key)
 * @returns Human-readable role name
 */
export function formatRoleForInsights(role: string): string {
  if (role === '*') return 'All Roles';
  
  // Check if it's a valid JobRole enum key
  if (role in JobRole) {
    return roleDisplayNames[role as JobRole];
  }
  
  // If it's already a display name or unknown, return as-is
  return role;
}

/**
 * Determines the display mode based on the insights data
 * @param insights Calculated insights data
 * @returns Display mode for UI rendering
 */
export function determineDisplayMode(insights: JobInsightsData): InsightsDisplayMode {
  // No jobs found
  if (insights.totalActiveJobs === 0) {
    return 'no_jobs';
  }
  
  // No recent jobs but has total jobs
  if (insights.recentJobsCount === 0 && insights.totalActiveJobs > 0) {
    return 'no_recent_jobs';
  }
  
  // Single company selected
  if (insights.isSingleCompany) {
    return 'single_company';
  }
  
  // Large dataset (1000+ jobs)
  if (insights.totalActiveJobs >= DEFAULT_CONFIG.largeDatasetThreshold) {
    return 'large_dataset';
  }
  
  // Normal display
  return 'normal';
}

/**
 * Handles pluralization for display text
 * @param count Number to check
 * @param singular Singular form
 * @param plural Plural form (optional, defaults to singular + 's')
 * @returns Properly pluralized string
 */
export function pluralize(count: number, singular: string, plural?: string): string {
  if (count === 1) {
    return `${count} ${singular}`;
  }
  return `${count} ${plural || singular + 's'}`;
}

/**
 * Validates job data for insights calculation
 * @param jobs Array of job listings
 * @returns Array of validation errors (empty if valid)
 */
export function validateJobData(jobs: JobListing[]): string[] {
  const errors: string[] = [];
  
  if (!Array.isArray(jobs)) {
    errors.push('Jobs data must be an array');
    return errors;
  }
  
  jobs.forEach((job, index) => {
    if (!job.company_name) {
      errors.push(`Job at index ${index} missing company_name`);
    }
    
    if (!job.id) {
      errors.push(`Job at index ${index} missing id`);
    }
    
    if (job.date_posted && isNaN(new Date(job.date_posted).getTime())) {
      errors.push(`Job at index ${index} has invalid date_posted: ${job.date_posted}`);
    }
  });
  
  return errors;
}
