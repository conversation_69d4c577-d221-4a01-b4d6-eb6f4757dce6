{"name": "first-2-apply", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@stripe/stripe-js": "^7.0.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/ssr": "^0.5.1", "@supabase/supabase-js": "^2.45.4", "@vercel/analytics": "^1.5.0", "axios": "^1.7.5", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "framer-motion": "^11.15.0", "lucide-react": "^0.435.0", "next": "^14.2.14", "node-cache": "^5.1.2", "openai": "^4.56.0", "p-throttle": "^6.2.0", "react": "^18", "react-confetti": "^6.2.2", "react-dom": "^18", "react-hot-toast": "^2.4.1", "react-icons": "^5.3.0", "react-simple-typewriter": "^5.0.1", "sonner": "^1.7.2", "stripe": "^18.0.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@shadcn/ui": "^0.0.4", "@types/node": "^20.0.0", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.6", "postcss": "^8", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "tsx": "^4.19.0", "typescript": "^5.5.4"}, "type": "module"}