// designSystem.ts

/**
 * Design system for First to Apply
 * A refined, sophisticated design system with muted tones optimized for job search and recruitment
 */

export const designSystem = {
  colors: {
    // Primary palette - Muted blue
    primary: "#3B82F6", // Muted blue
    primaryLight: "#60A5FA",
    primaryDark: "#2563EB",
    
    // Accent colors - Sophisticated teal
    accent: "#14B8A6", // Muted teal
    accentLight: "#2DD4BF",
    accentDark: "#0D9488",
    
    // Neutral colors - Enhanced for better hierarchy
    background: "#FFFFFF",
    backgroundAlt: "#F8FAFC",
    surface: "#FFFFFF",
    surfaceAlt: "rgba(255, 255, 255, 0.9)",
    surfaceAccent: "#F0F9FF", // Subtle blue tint for accented surfaces
    
    // Text colors - Refined for better readability
    text: "#1E293B",
    textSecondary: "#475569",
    textMuted: "#64748B",
    textOnPrimary: "#FFFFFF",
    
    // Border colors
    border: "#E2E8F0",
    borderLight: "#F1F5F9",
    borderHover: "#CBD5E1",
    borderFocus: "#3B82F6",
    
    // Status colors - More sophisticated
    success: "#059669",
    error: "#DC2626",
    warning: "#D97706",
    info: "#3B82F6",
    
    // Semantic colors - Aligned with new palette
    new: "#3B82F6",
    active: "#059669",
    closed: "#64748B"
  },

  gradients: {
    // Core gradients - More subtle transitions
    primary: "linear-gradient(135deg, #3B82F6, #60A5FA)",
    secondary: "linear-gradient(135deg, #14B8A6, #2DD4BF)",
    hero: "linear-gradient(135deg, #3B82F6, #14B8A6)",
    text: "linear-gradient(135deg, #3B82F6 0%, #14B8A6 100%)",
    
    // Background gradients - Softer
    background: "linear-gradient(135deg, #F8FAFC, #EFF6FF, #F0FDFA)",
    subtle: "linear-gradient(to right, rgba(59, 130, 246, 0.1), rgba(20, 184, 166, 0.1))",
    card: "linear-gradient(to bottom, #FFFFFF, #F8FAFC)"
  },

  typography: {
    fontFamily: {
      display: 'Inter, system-ui, sans-serif',
      body: 'Inter, system-ui, sans-serif',
    },
    
    sizes: {
      xs: '0.75rem',     // 12px
      sm: '0.875rem',    // 14px
      base: '1rem',      // 16px
      lg: '1.125rem',    // 18px
      xl: '1.25rem',     // 20px
      '2xl': '1.5rem',   // 24px
      '3xl': '1.875rem', // 30px
      '4xl': '2.25rem',  // 36px
      '5xl': '3rem',     // 48px
      '6xl': '3.75rem',  // 60px
    },
    
    weights: {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
    },
    
    lineHeights: {
      none: '1',
      tight: '1.25',
      snug: '1.375',
      normal: '1.5',
      relaxed: '1.625',
      loose: '2',
    },
    
    patterns: {
      hero: {
        primary: {
          fontSize: '3.75rem', // text-6xl
          fontWeight: '700',   // font-bold
          background: 'linear-gradient(135deg, #3B82F6, #14B8A6)',
          backgroundClip: 'text',
          textFillColor: 'transparent'
        },
        secondary: {
          fontSize: '1.25rem', // text-xl
          color: '#475569',    // textSecondary
          lineHeight: '1.75'   // leading-7
        }
      },
      section: {
        title: {
          fontSize: '3rem',    // text-5xl
          fontWeight: '700',   // font-bold
          background: 'linear-gradient(135deg, #3B82F6, #14B8A6)',
          backgroundClip: 'text',
          textFillColor: 'transparent'
        },
        subtitle: {
          fontSize: '1.125rem', // text-lg
          color: '#64748B',     // textMuted
          lineHeight: '1.75'    // leading-7
        }
      }
    }
  },

  spacing: {
    0: '0',
    1: '0.25rem',    // 4px
    2: '0.5rem',     // 8px
    3: '0.75rem',    // 12px
    4: '1rem',       // 16px - Standard spacing
    5: '1.25rem',    // 20px
    6: '1.5rem',     // 24px
    8: '2rem',       // 32px
    10: '2.5rem',    // 40px
    12: '2.75rem',   // 44px
    16: '3.5rem',    // 56px
    20: '4rem',      // 64px
    24: '5rem',      // 80px
  },

  borderRadius: {
    none: '0',
    sm: '0.25rem',    // 4px
    DEFAULT: '0.375rem', // 6px
    md: '0.5rem',     // 8px
    lg: '0.75rem',    // 12px
    xl: '1rem',       // 16px
    '2xl': '1.5rem',  // 24px
    pill: '9999px',
  },

  shadows: {
    none: 'none',
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    DEFAULT: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    hover: '0 2px 4px rgba(0,0,0,0.05)',
    focus: '0 0 0 3px rgba(59, 130, 246, 0.25)', // New focus ring shadow
  },

  effects: {
    glass: {
      light: {
        background: 'rgba(255, 255, 255, 0.8)',
        backdropFilter: 'blur(12px)',
      },
      dark: {
        background: 'rgba(30, 41, 59, 0.8)', // Updated to match new text color
        backdropFilter: 'blur(12px)',
      }
    },
    transitions: {
      default: 'all 0.2s ease-in-out',
      fast: 'all 0.1s ease-in-out',
      slow: 'all 0.3s ease-in-out',
    }
  },

  components: {
    buttons: {
      primary: {
        default: {
          background: "linear-gradient(135deg, #3B82F6, #14B8A6)",
          color: "#FFFFFF",
          padding: "0.625rem 1.25rem",
          borderRadius: "0.5rem",
          fontWeight: "500",
          border: "none",
          cursor: "pointer",
          transition: "all 0.2s ease"
        },
        hover: {
          opacity: "0.95",
          transform: "translateY(-1px)"
        }
      },
      secondary: {
        default: {
          background: "rgba(255, 255, 255, 0.9)",
          color: "#475569",
          padding: "0.625rem 1.25rem",
          borderRadius: "0.5rem",
          fontWeight: "500",
          border: "1px solid #E2E8F0",
          cursor: "pointer",
          transition: "all 0.2s ease"
        },
        hover: {
          borderColor: "#CBD5E1",
          background: "rgba(255, 255, 255, 1)",
          transform: "translateY(-1px)"
        }
      }
    },
    tags: {
      company: {
        default: {
          padding: "0.375rem 0.75rem",
          background: "rgba(255, 255, 255, 0.9)",
          border: "1px solid #E2E8F0",
          borderRadius: "9999px",
          fontSize: "0.875rem",
          color: "#475569",
          fontWeight: "500",
          cursor: "pointer",
          transition: "all 0.2s ease",
          display: "flex",
          alignItems: "center",
          gap: "0.5rem"
        },
        hover: {
          background: "rgba(255, 255, 255, 1)",
          borderColor: "#CBD5E1",
          transform: "translateY(-1px)",
          boxShadow: "0 2px 4px rgba(0,0,0,0.05)"
        },
        active: {
          background: "rgba(59, 130, 246, 0.1)",
          borderColor: "#3B82F6",
          color: "#3B82F6"
        }
      }
    },
    inputs: {
      search: {
        default: {
          padding: "0.625rem 0.875rem",
          borderRadius: "0.5rem",
          border: "1px solid #E2E8F0",
          backgroundColor: "rgba(255, 255, 255, 0.9)",
          fontSize: "0.875rem",
          color: "#1E293B",
          outline: "none",
          transition: "all 0.2s ease"
        },
        focus: {
          borderColor: "#3B82F6",
          boxShadow: "0 0 0 3px rgba(59, 130, 246, 0.1)"
        }
      }
    },
    cards: {
      glass: {
        background: "rgba(255, 255, 255, 0.8)",
        backdropFilter: "blur(12px)",
        borderRadius: "1rem",
        border: "1px solid rgba(226, 232, 240, 0.7)",
        boxShadow: "0 4px 6px rgba(0, 0, 0, 0.05)"
      }
    }
  },

  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  }
} as const;

// Type exports
export type Colors = typeof designSystem.colors;
export type Gradients = typeof designSystem.gradients;
export type Typography = typeof designSystem.typography;
export type Spacing = typeof designSystem.spacing;
export type BorderRadius = typeof designSystem.borderRadius;
export type Shadows = typeof designSystem.shadows;
export type Effects = typeof designSystem.effects;
export type Components = typeof designSystem.components;
export type Breakpoints = typeof designSystem.breakpoints;

// Utility types
export type Color = keyof Colors;
export type Gradient = keyof Gradients;
export type FontSize = keyof Typography['sizes'];
export type FontWeight = keyof Typography['weights'];
export type LineHeight = keyof Typography['lineHeights'];
export type Space = keyof Spacing;
export type Radius = keyof BorderRadius;
export type Shadow = keyof Shadows;
export type Breakpoint = keyof Breakpoints;