/**
 * TypeScript interfaces for job insights data structures
 * Used by the HiringInsightsCard component to display aggregated job market intelligence
 */

// Individual company activity data
export interface CompanyActivity {
  /** Company name */
  name: string;
  /** Number of jobs posted by this company */
  jobCount: number;
  /** Number of jobs posted in the last 14 days */
  recentJobCount: number;
  /** Company logo URL (optional) */
  logoUrl?: string | null;
}

// Aggregated insights data for the insights card
export interface JobInsightsData {
  /** Selected role for display */
  role: string;
  /** Display-formatted role name (e.g., "Product Manager" instead of "PRODUCT_MANAGER") */
  roleDisplayName: string;
  /** Total number of active jobs across all selected companies */
  totalActiveJobs: number;
  /** Number of jobs posted in the last 14 days */
  recentJobsCount: number;
  /** Number of companies that have active jobs */
  companiesWithJobs: number;
  /** Total number of selected companies (including those with no jobs) */
  totalSelectedCompanies: number;
  /** Array of company activity data, sorted by job count (descending) */
  companyActivities: CompanyActivity[];
  /** Whether this is a single company selection (affects display format) */
  isSingleCompany: boolean;
  /** Whether there are more companies than can be displayed in the top activity section */
  hasMoreCompanies: boolean;
  /** Number of additional companies not shown in top activity */
  additionalCompaniesCount: number;
}

// Edge case types for different display scenarios
export type InsightsDisplayMode = 
  | 'normal'           // Standard display with multiple companies
  | 'single_company'   // Single company selected
  | 'no_jobs'         // No jobs found
  | 'no_recent_jobs'  // No jobs in last 14 days but has total jobs
  | 'large_dataset';  // 1000+ jobs, show top companies only

// Configuration for insights calculation
export interface InsightsConfig {
  /** Number of days to consider for "recent" jobs (default: 14) */
  recentDaysThreshold: number;
  /** Maximum number of companies to show in top activity (default: 3) */
  maxTopCompanies: number;
  /** Threshold for large dataset handling (default: 1000) */
  largeDatasetThreshold: number;
}

// Input data structure for insights calculation
export interface JobInsightsInput {
  /** Array of job listings to analyze */
  jobs: JobListing[];
  /** Selected companies for context */
  selectedCompanies: string[];
  /** Selected role(s) */
  selectedRole: string[];
  /** Company logo cache for display */
  logoCache?: Record<string, string | null>;
  /** Configuration options */
  config?: Partial<InsightsConfig>;
}

// Job listing structure (matches existing interface from CompanySelectionSection)
export interface JobListing {
  id: number;
  title: string;
  company_name: string;
  category_dept: string;
  date_posted: string;
  years_of_experience: number | null;
  location: string;
  url: string;
  companies?: {
    id: number;
    name: string;
    logo_url: string;
    careers_page_url?: string;
  } | null;
}

// Props interface for the HiringInsightsCard component
export interface HiringInsightsCardProps {
  /** Insights data to display */
  insights: JobInsightsData;
  /** Whether user is authenticated (affects button text and behavior) */
  isAuthenticated: boolean;
  /** Callback when Create Alert button is clicked (authenticated users) */
  onCreateAlert?: () => void;
  /** Callback when Sign up button is clicked (unauthenticated users) */
  onSignupClick?: () => void;
  /** Loading state */
  loading?: boolean;
  /** Additional CSS classes */
  className?: string;
}

// Return type for insights calculation functions
export interface InsightsCalculationResult {
  /** Calculated insights data */
  insights: JobInsightsData;
  /** Display mode determined by the data */
  displayMode: InsightsDisplayMode;
  /** Any errors encountered during calculation */
  errors?: string[];
}
