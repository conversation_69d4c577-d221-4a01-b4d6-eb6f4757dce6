# Production Deployment Checklist

## Must Do:

1. Supabase Configuration:
   - [ ] Update "Site URL" in Supabase dashboard to production domain (e.g., https://www.yourdomain.com)
   - [ ] Add production callback URL to "Redirect URLs" (e.g., https://www.yourdomain.com/auth/callback)

2. Environment Variables:
   - [ ] Set NEXT_PUBLIC_SUPABASE_URL to production Supabase URL
   - [ ] Set NEXT_PUBLIC_SUPABASE_ANON_KEY to production Supabase anon key
   - [ ] Set NEXT_PUBLIC_SITE_URL to production domain

3. Code Updates:
   - [ ] Ensure emailRedirectTo in signup/page.tsx uses NEXT_PUBLIC_SITE_URL:
     ```typescript
     emailRedirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`,
     ```
   - [ ] Create a separate utility file for the `standardizeRoleFilter` function to simplify code maintenance and reuse.
   - Right now this is in signup.tsx, but it's used in multiple places, so it should be in a new file called utils.ts or something similar, this was part of the data clean up when did to conver everything to arrays. IMPORTANT! 

4. SSL Certificate:
   - [ ] Ensure production site uses HTTPS

5. Testing:
   - [ ] Test full authentication flow in production environment
   - [ ] Verify user profile creation and filter application post-authentication

6. Handling signing back in
   - [] Ensure that I understand how sessions and cookies work, and that users are not signed out too often when they signup with a magic link and when they are, that they can easliy relogin

7. Add signin with google on signup page

8. Update email verification email to not come from supabase, but from us, so that our email provider is used and users know it's us asking for verification

9. Update supabase url configuration with the proper site url and callback urls
   - Do the same for Google OAuth and anything else that needs to be configured

9. Legal Pages Setup:
   - [ ] Review and update Privacy Policy content with actual policies
   - [ ] Review and update Terms of Service content with actual terms
   - [ ] Ensure legal pages are accessible in both development and production
   - [ ] Update support email address in legal pages
   - [ ] Consider legal review of final policies and terms

10. Google OAuth Configuration:
   - [ ] Update authorized domains in Google Cloud Console for production
   - [ ] Update OAuth consent screen with production URLs
   - [ ] Verify all redirect URIs are properly configured
   - [ ] Test Google Sign-In flow in production environment
   - [ ] Ensure proper error handling for Google Sign-In failures

11. User Data & Privacy:
   - [ ] Implement proper data retention policies
   - [ ] Ensure user data handling complies with privacy policy
   - [ ] Set up proper logging for authentication attempts
   - [ ] Configure data backup procedures

12. Google OAuth Verification Requirements:
   - [ ] Prepare application verification materials:
     - [ ] Record demonstration video of OAuth implementation
     - [ ] Write detailed justification for each OAuth scope
     - [ ] Prepare documentation of data usage and security practices
   - [ ] Submit app for Google verification
   - [ ] Plan for verification review period (can take several days/weeks)
   - [ ] Have backup authentication method ready during review

13. OAuth Testing Pre-Verification:
   - [ ] Test OAuth flow with all test users
   - [ ] Verify OAuth scopes are minimal and necessary
   - [ ] Document all OAuth-related error scenarios and handling
   - [ ] Test OAuth flow across different devices/browsers
   - [ ] Verify OAuth state management and security measures

14. OAuth Production Launch:
   - [ ] Monitor OAuth usage and quotas
   - [ ] Set up alerts for OAuth-related errors
   - [ ] Document OAuth maintenance procedures
   - [ ] Create user support documentation for OAuth issues
   - [ ] Plan for OAuth scope changes and reauthorization process

15. Performance Optimization:
   - [ ] Implement query optimization:
     - [ ] Add proper database indexes
     - [ ] Add limit and ordering to job listing queries
     - [ ] Implement pagination for large result sets
   - [ ] Add caching strategy:
     - [ ] Configure proper cache headers for API routes
     - [ ] Implement image optimization with blur placeholders
     - [ ] Set up CDN for static assets
   - [ ] Code optimization:
     - [ ] Enable Next.js performance analytics
     - [ ] Run Lighthouse audit and address critical issues
     - [ ] Implement proper loading states for better UX

16. Stripe Production Checks:
   - [ ] Verify all Stripe emails are being received (receipts, subscription confirmations)
   - [ ] Confirm webhook endpoint is properly configured in Stripe Dashboard
   - [ ] Test subscription cancellation flow through customer portal
   - [ ] Monitor webhook delivery in Stripe Dashboard for first few subscriptions
   - [ ] Document process for handling failed webhooks and subscription sync issues

## Maybe (Depending on Your Setup):

1. CORS Configuration:
   - [ ] Update CORS settings if your API and frontend are on different domains

2. Security Headers:
   - [ ] Implement appropriate security headers (e.g., Content Security Policy)

3. NextAuth Configuration:
   - [ ] Update NextAuth config if you're using it alongside Supabase

4. Database Migrations:
   - [ ] Run any necessary database migrations for production

5. Error Logging:
   - [ ] Set up error logging service for production environment

6. Performance Monitoring:
   - [ ] Implement performance monitoring tools

7. Backup Strategy:
   - [ ] Ensure regular backups of production database are configured

Remember to thoroughly test all authentication flows, user profile creation, and filter application in a staging environment that mirrors your production setup before going live.

