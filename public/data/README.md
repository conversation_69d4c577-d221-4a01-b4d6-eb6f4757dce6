# Bundle Configuration

This directory contains the JSON configuration for company bundles displayed on the dashboard.

## File Structure

### `bundles.json`

This file defines the company bundles that users can select from. The structure is:

```json
{
  "bundles": [
    {
      "id": "unique-bundle-id",
      "title": "Bundle Display Name",
      "description": "Brief description shown to users (optional)",
      "category": "bundle-category (optional)",
      "companies": [
        "Company Name 1",
        "Company Name 2",
        "..."
      ]
    }
  ]
}
```

## Field Descriptions

- **id** (optional): Unique identifier for the bundle. Used for tracking and keys.
- **title** (required): The name displayed to users in the bundle card.
- **description** (optional): A brief description shown below the title.
- **category** (optional): Used for grouping or filtering bundles.
- **companies** (required): Array of company names exactly as they appear in your company database.

## Adding New Bundles

1. Open `bundles.json`
2. Add a new object to the `bundles` array
3. Fill in the required fields (`title` and `companies`)
4. Optionally add `description` and other fields
5. Save the file - changes will be reflected on the next page load

## Example

```json
{
  "bundles": [
    {
      "id": "remote-companies",
      "title": "Top Remote Companies",
      "description": "Companies with excellent remote work culture",
      "category": "remote",
      "companies": [
        "GitLab",
        "Buffer",
        "Zapier",
        "Automattic",
        "InVision",
        "Toptal",
        "Remote Year"
      ]
    }
  ]
}
```

## Notes

- Company names must match exactly with names in your company database
- If JSON loading fails, the system will fall back to hardcoded bundles
- The system supports any number of bundles
- Bundle order in the JSON file determines display order 