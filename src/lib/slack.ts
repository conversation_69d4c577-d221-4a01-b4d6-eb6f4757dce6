export async function sendSlackNotification(message: string) {
  try {
    const response = await fetch('/api/notify-slack', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ message }),
    })

    if (!response.ok) {
      throw new Error('Failed to send notification')
    }
  } catch (error) {
    console.error('Error sending Slack notification:', error)
  }
}