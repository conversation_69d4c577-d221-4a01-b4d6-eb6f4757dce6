// Role type definitions and display utilities
export enum JobRole {
  PRODUCT_MANAGER = "PRODUCT_MANAGER",
  SOFTWARE_ENGINEER = "SOFTWARE_ENGINEER",
  PRODUCT_DESIGNER = "PRODUCT_DESIGNER",
  DATA_ENGINEER = "DATA_ENGINEER",
  DATA_SCIENTIST = "DATA_SCIENTIST",
  SALES = "SALES",
  CUSTOMER_SUCCESS = "CUSTOMER_SUCCESS",
  CUSTOMER_SUPPORT = "CUSTOMER_SUPPORT",
  FINANCE = "FINANCE",
  MARKETING = "MARKETING",
  LEGAL = "LEGAL",
  HUMAN_RESOURCES = "HUMAN_RESOURCES",
  OTHER = "OTHER"
}

// Display mappings (what users see)
export const roleDisplayNames: Record<JobRole, string> = {
  [JobRole.PRODUCT_MANAGER]: "Product Manager",
  [JobRole.SOFTWARE_ENGINEER]: "Software Engineer",
  [JobRole.PRODUCT_DESIGNER]: "Product Designer",
  [JobRole.DATA_ENGINEER]: "Data Engineer",
  [JobRole.DATA_SCIENTIST]: "Data Scientist",
  [JobRole.SALES]: "Sales",
  [JobRole.CUSTOMER_SUCCESS]: "Customer Success",
  [JobRole.CUSTOMER_SUPPORT]: "Customer Support",
  [JobRole.FINANCE]: "Finance",
  [JobRole.MARKETING]: "Marketing",
  [JobRole.LEGAL]: "Legal",
  [JobRole.HUMAN_RESOURCES]: "Human Resources",
  [JobRole.OTHER]: "Other"
} as const;

// Utility function to format role for display
export function formatRoleForDisplay(role: string | string[] | null): string {
  if (!role) return 'All Roles';
  
  // Handle array format
  if (Array.isArray(role)) {
    if (role.length === 0 || role[0] === '*') return 'All Roles';
    const cleanRole = role[0].replace(/[\[\]"']/g, '').trim();
    return getRoleDisplay(cleanRole);
  }
  
  // Handle string format
  const cleanRole = role.replace(/[\[\]"']/g, '').trim();
  return getRoleDisplay(cleanRole);
}

// Helper function to get display value
function getRoleDisplay(role: string): string {
  if (role === '*') return 'All Roles';
  
  // Try to match with enum values
  const enumKey = Object.entries(JobRole).find(([_, value]) => 
    value.toLowerCase() === role.toLowerCase()
  );

  if (enumKey) {
    return roleDisplayNames[enumKey[1] as JobRole];
  }

  // Fallback: Capitalize first letter of each word
  return role
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}
