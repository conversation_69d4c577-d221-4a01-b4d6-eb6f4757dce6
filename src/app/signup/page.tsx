"use client";

import * as React from "react";
import { useState, useEffect, Suspense } from "react";
import Link from "next/link";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { createClient } from '@/lib/supabase'
import { useRouter, useSearchParams } from "next/navigation";
import { FcGoogle } from 'react-icons/fc';
import { formatRoleForDisplay } from '@/lib/roles'
import { Sparkles } from 'lucide-react';
import { inject } from '@vercel/analytics';
import { CompanyLogo } from '@/components/ui/CompanyLogo';

interface Filters {
  companies: string[];
  role: string[];
  experience?: string;
  location?: string;
  alert_frequency?: string[];
}

// Add this helper function near the top of the file, after imports
const standardizeRoleFilter = (role: string | string[] | null): string[] => {
  if (!role) return [];
  if (role === 'all') return ['*'];
  if (Array.isArray(role)) return role;
  return [role];
};

// Separate the part that uses useSearchParams
function SignUpContent() {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<Filters | null>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [companyLogos, setCompanyLogos] = useState<Record<string, string | null>>({});
  const router = useRouter();
  const searchParams = useSearchParams();

  // Detect if user is on mobile device
  useEffect(() => {
    const checkIfMobile = () => {
      const userAgent = window.navigator.userAgent.toLowerCase();
      const mobileRegex = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i;
      setIsMobile(mobileRegex.test(userAgent) || window.innerWidth <= 768);
    };

    checkIfMobile();
    window.addEventListener('resize', checkIfMobile);
    
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  useEffect(() => {
    const filtersParam = searchParams.get('filters');
    if (filtersParam) {
      try {
        const decodedFilters = JSON.parse(decodeURIComponent(filtersParam));
        setFilters(decodedFilters);
      } catch (error) {
        console.error("Error parsing filters:", error);
      }
    }
  }, [searchParams]);

  // Fetch company logos when filters change
  useEffect(() => {
    async function fetchCompanyLogos() {
      if (!filters || filters.companies.length === 0) return;
      
      try {
        const supabase = createClient();
        const { data, error } = await supabase
          .from('companies')
          .select('name, logo_url')
          .in('name', filters.companies);

        if (error) {
          console.error('Error fetching company logos:', error);
          return;
        }

        const logoMap: Record<string, string | null> = {};
        data?.forEach(company => {
          logoMap[company.name] = company.logo_url;
        });
        setCompanyLogos(logoMap);
      } catch (error) {
        console.error('Error fetching company logos:', error);
      }
    }

    fetchCompanyLogos();
  }, [filters]);

  useEffect(() => {
    inject(); // Initialize analytics
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    const supabase = createClient()

    try {
      // Store filters in temporary table
      if (filters) {
        const standardizedFilters = {
          ...filters,
          role: standardizeRoleFilter(filters.role),
          include_future_roles: filters.role.includes('*'),
          include_undefined_roles: filters.role.includes('*')
        };

        const { error: tempFilterError } = await supabase
          .from('temp_user_filters')
          .upsert({ 
            email, 
            filters: standardizedFilters
          });

        console.log('Storing filters:', filters);

        if (tempFilterError) throw tempFilterError;
      }

      // Sign up the user with Supabase Auth
      console.log('Attempting to sign in with OTP', { email, redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback` });
      const { error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          emailRedirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`,
          data: {
            name,
          },
        },
      });
      console.log('OTP sign-in result:', error ? 'Error' : 'Success');

      if (error) throw error;

      // Redirect to a confirmation page
      router.push('/signup-success');
    } catch (error) {
      console.error('Error in signup:', error);
      if (error instanceof Error) {
        setError(error.message);
      } else {
        setError("An unknown error occurred");
      }
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setLoading(true);
    setError(null);
    const supabase = createClient();

    try {
      // Store filters first if they exist
      if (filters) {
        const standardizedFilters = {
          ...filters,
          role: standardizeRoleFilter(filters.role),
          include_future_roles: filters.role.includes('*'),
          include_undefined_roles: filters.role.includes('*')
        };

        const { error: tempFilterError } = await supabase
          .from('temp_user_filters')
          .upsert({ 
            email: 'pending_google_auth', // We'll update this after auth
            filters: standardizedFilters
          });

        if (tempFilterError) throw tempFilterError;
      }

      // Initiate Google sign-in
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        },
      });

      if (error) throw error;
    } catch (error) {
      console.error('Error in Google signup:', error);
      if (error instanceof Error) {
        setError(error.message);
      } else {
        setError("An unknown error occurred");
      }
      setLoading(false);
    }
  };

  return (
    <main className="flex min-h-screen">
      <div className="absolute top-4 left-4 lg:top-6 lg:left-6 z-10">
        <Link 
          href="/" 
          className="text-xl lg:text-2xl font-bold text-gray-900 hover:text-[#4338CA] transition-colors"
        >
          Awaloon
        </Link>
      </div>

      <div className="flex flex-col lg:flex-row w-full min-h-screen">
        {/* Left Side - Signup Form */}
        <div className="flex-1 flex items-center justify-center px-4 py-6 lg:py-12 bg-gradient-to-br from-indigo-500/12 via-purple-100/50 to-purple-200/60">
          <div className="w-full max-w-sm lg:max-w-md">
            <div className="bg-white rounded-2xl p-6 lg:p-7 shadow-xl border border-white/70 backdrop-blur-sm">
              <h1 className="text-2xl lg:text-3xl font-bold mb-5 text-gray-900 text-center">Sign Up</h1>
              {error && (
                <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-red-600 text-sm text-center">{error}</p>
                </div>
              )}
              
              <button
                onClick={handleGoogleSignIn}
                disabled={loading}
                className="w-full flex items-center justify-center gap-3 px-4 py-3 bg-white hover:bg-gray-50 text-gray-900 rounded-xl transition-all hover:-translate-y-0.5 shadow-sm border border-gray-200 mb-5 font-medium"
              >
                <FcGoogle className="text-xl" />
                Continue with Google
              </button>

              {!isMobile && (
                <>
                  <div className="relative mb-5">
                    <div className="absolute inset-0 flex items-center">
                      <div className="w-full border-t border-gray-200"></div>
                    </div>
                    <div className="relative flex justify-center text-sm">
                      <span className="px-4 bg-white text-gray-500">Or continue with email</span>
                    </div>
                  </div>

                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div>
                      <Input
                        type="text"
                        placeholder="Full Name"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        required
                        className="px-4 py-3 bg-gray-50/50 border-gray-200 rounded-xl transition-all text-base focus:outline-none focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-500 w-full"
                      />
                    </div>
                    <div>
                      <Input
                        type="email"
                        placeholder="Email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        required
                        className="px-4 py-3 bg-gray-50/50 border-gray-200 rounded-xl transition-all text-base focus:outline-none focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-500 w-full"
                      />
                    </div>
                    <button
                      type="submit"
                      disabled={loading}
                      className="w-full px-4 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white rounded-xl transition-all hover:-translate-y-0.5 shadow-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {loading ? "Signing up..." : "Sign up with Email"}
                    </button>
                  </form>
                </>
              )}

              <p className="mt-5 text-center text-gray-600 text-sm">
                Already have an account?{" "}
                <Link 
                  href="/signin" 
                  className="text-indigo-600 hover:text-purple-600 font-medium transition-colors"
                >
                  Sign in
                </Link>
              </p>
            </div>
          </div>
        </div>

        {/* Right Side / Bottom - Secondary FYI Preview */}
        {filters && (filters.companies.length > 0 || filters.role.length > 0) && (
          <div className="flex-1 flex items-start lg:items-center justify-center px-4 py-3 lg:py-12 lg:pr-8 bg-gradient-to-br from-purple-50 via-indigo-50 to-purple-100/40">
            <div className="w-full max-w-md lg:max-w-xl">
              <div className="bg-white/80 backdrop-blur-sm border border-purple-200/40 rounded-xl shadow-md">
                <div className="p-4 lg:p-6">
                  {/* Enhanced Header */}
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-8 h-8 lg:w-10 lg:h-10 bg-gradient-to-br from-purple-100 to-indigo-100 rounded-full flex items-center justify-center">
                      <Sparkles className="w-4 h-4 lg:w-5 lg:h-5 text-purple-600" />
                    </div>
                    <div>
                      <h3 className="text-sm lg:text-base font-semibold text-gray-800">{"Here's what you'll be tracking"}</h3>
                    </div>
                  </div>

                  {/* Enhanced Alert Summary */}
                  <div className="bg-gradient-to-r from-purple-50 to-indigo-50/30 rounded-lg p-3 lg:p-4 mb-4">
                    <div className="flex items-center gap-2 mb-3">
                      <span className="text-sm lg:text-base text-gray-700">Tracking</span>
                      <span className="inline-flex items-center px-3 py-1 lg:px-4 lg:py-1.5 rounded-full text-sm font-medium bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-sm">
                        {formatRoleForDisplay(filters.role)}
                      </span>
                      <span className="text-sm lg:text-base text-gray-700">roles at:</span>
                    </div>

                    {/* Enhanced Company Grid */}
                    <div className="grid grid-cols-2 lg:grid-cols-3 gap-2 lg:gap-3">
                      {filters.companies.map((company) => (
                        <div 
                          key={company}
                          className="flex items-center gap-2 lg:gap-3 p-2 lg:p-3 bg-white rounded-lg border border-purple-100 shadow-sm"
                        >
                          <div className="w-5 h-5 lg:w-6 lg:h-6 rounded overflow-hidden flex-shrink-0 bg-gray-50">
                            <CompanyLogo
                              name={company}
                              logoUrl={companyLogos[company] || null}
                              size={24}
                            />
                          </div>
                          <span className="text-xs lg:text-sm font-medium text-gray-900 truncate">{company}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Enhanced Benefits */}
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm lg:text-base text-gray-600">
                      <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                      <span>Instant notifications for new roles</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm lg:text-base text-gray-600">
                      <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                      <span>Be first to apply</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Mobile: Enhanced background when no filters */}
        {!filters && (
          <div className="lg:hidden bg-gradient-to-br from-purple-50 to-indigo-50 py-2">
            {/* Minimal space for visual balance */}
          </div>
        )}
      </div>
    </main>
  );
}

// Main component with Suspense boundary
export default function SignUp() {
  return (
    <Suspense fallback={
      <div className="flex min-h-screen items-center justify-center">
        <div className="animate-pulse">Loading...</div>
      </div>
    }>
      <SignUpContent />
    </Suspense>
  );
}
