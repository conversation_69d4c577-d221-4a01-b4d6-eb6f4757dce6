import type { Metadata, Viewport } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Providers } from "@/components/Providers";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "<PERSON>waloon - Be First to Apply",
  description: "Get notified instantly about jobs at companies you love",
  icons: {
    icon: [
      { url: '/icons/favicon-16x16.png', sizes: '16x16' },
      { url: '/icons/favicon-32x32.png', sizes: '32x32' },
      { url: '/icons/favicon.ico' }
    ],
    apple: [
      { url: '/icons/apple-touch-icon.png' }
    ],
    other: [
      {
        rel: 'mask-icon',
        url: '/icons/android-chrome-512x512.png',
        color: '#4F46E5'
      }
    ]
  },
  manifest: '/manifest.json',
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1.0,
  themeColor: '#4F46E5',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
