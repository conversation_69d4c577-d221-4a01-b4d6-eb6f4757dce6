import { NextResponse } from "next/server";
import { supabaseAdmin } from "@/lib/supabaseAdmin";

export async function GET() {
    const { data, error } = await supabaseAdmin
        .from("job_listings")
        .select("id, title, company_name, category_dept, date_posted, years_of_experience, location, url")
        .gt('date_posted', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
        .limit(500);

    if (error) {
        console.error("Supabase error:", error);
        return NextResponse.json({ error: "Failed to fetch jobs" }, { status: 500 });
    }

    return NextResponse.json(data);
}