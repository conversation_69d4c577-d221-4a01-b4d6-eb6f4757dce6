import { NextResponse } from 'next/server';
import OpenAI from 'openai';

// Base URL for Exa API
const EXA_API_URL = 'https://api.exa.ai/search';
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

// Format company name for search
const formatCompanyName = (name: string) => {
  if (name.toLowerCase().includes('google deepmind')) {
    return ['Google DeepMind', 'DeepMind'];
  }
  return [name];
};

// Role types and their weights
const ROLE_WEIGHTS = {
  SENIOR_DEPARTMENT: 1.0,  // director, head, principal, lead in department
  MID_DEPARTMENT: 0.8,    // senior, manager in department
  DEPARTMENT_IC: 0.7,     // Individual contributors in the department
  RECRUITER: 0.5         // recruiting/talent roles
} as const;

export async function POST(req: Request) {
  try {
    const { jobTitle, companyName, department } = await req.json();
    console.log('Received request:', { jobTitle, companyName, department });

    if (!jobTitle || !companyName) {
      return NextResponse.json(
        { error: 'Job title and company name are required' },
        { status: 400 }
      );
    }

    if (!process.env.EXA_API_KEY || !process.env.OPENAI_API_KEY) {
      throw new Error('API keys not configured');
    }

    // Build search query focusing on department leaders first
    const companyVariations = formatCompanyName(companyName);
    const companyQuery = companyVariations.map(name => `"${name}"`).join(' OR ');
    
    // Search query now includes both leadership and IC roles
    const searchQuery = {
      query: `site:linkedin.com/in/ (${companyQuery}) AND ("current" OR "present" OR "full-time") AND (
        "${department} director" OR 
        "${department} lead" OR 
        "head of ${department}" OR 
        "principal ${department}" OR 
        "senior ${department}" OR
        "${department} manager" OR
        "${department} specialist" OR
        "${department} expert" OR
        "${department} recruiter" OR
        "talent acquisition ${department}" OR
        "recruiter ${department}" OR
        "${department} team" OR
        "${department} member"
      )`,
      text: true,
      highlights: true,
      summary: {
        query: `What is this person's current role at ${companyName}?`,
        length: 3
      },
      use_autoprompt: true,
      type: "keyword",
      include_domains: ["linkedin.com"],
      num_results: 15
    };

    const response = await fetch(EXA_API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.EXA_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(searchQuery),
    });

    if (!response.ok) {
      console.error('EXA API Error:', response.status, response.statusText);
      return NextResponse.json(
        { error: 'Failed to fetch hiring managers' },
        { status: response.status }
      );
    }

    const exaData = await response.json();
    console.log('Raw EXA response:', JSON.stringify(exaData, null, 2));

    // Format profiles for OpenAI analysis
    const profiles = exaData.results?.map((result: any) => {
      const fullTitle = result.title || '';
      let [name, ...titleParts] = fullTitle.split(/[-|]/);
      return {
        url: result.url,
        name: name.trim(),
        title: titleParts.join(' - ').trim() || result.summary || 'Unknown Title',
        profileText: result.text || '',
        highlights: result.highlights || [],
        summary: result.summary || ''
      };
    }).filter(Boolean);

    // Process with OpenAI
    const completion = await openai.chat.completions.create({
      messages: [
        {
          role: "system",
          content: "You are an expert recruiter analyzing LinkedIn profiles to find relevant contacts at the company. You must respond in JSON format only. Focus on current employment and department relevance. Include profiles even if they're not confirmed hiring managers, as long as they work in the relevant department. For relevance level, use one of: 'Super Relevant', 'Relevant', or 'Somewhat Relevant' based on how closely their role and experience match the position."
        },
        {
          role: "user",
          content: `Analyze these LinkedIn profiles and identify relevant contacts at ${companyName} for a ${jobTitle} position in the ${department} department.

Profiles to analyze:
${JSON.stringify(profiles, null, 2)}

Return profiles that are:
1. Currently employed at ${companyName}
2. In the ${department} department or related roles
3. Include at least 3 profiles if available, even if they're not confirmed hiring managers
4. Prioritize profiles that are likely to be involved in hiring, but include others if needed to reach 3 profiles

Your response must be valid JSON with this exact structure:
{
  "topProfiles": [
    {
      "name": string,
      "title": string,
      "url": string,
      "relevanceLevel": "Super Relevant" | "Relevant" | "Somewhat Relevant",
      "recommendationReason": string
    }
  ],
  "analysisNotes": string
}`
        }
      ],
      model: "gpt-4",
      temperature: 0.2
    });

    let openAIResponse;
    try {
      const content = completion.choices[0].message.content;
      if (!content) {
        throw new Error('OpenAI returned empty response');
      }
      openAIResponse = JSON.parse(content);
    } catch (error) {
      console.error('Error parsing OpenAI response:', error);
      throw new Error('Failed to parse hiring manager results');
    }

    console.log('OpenAI processed results:', openAIResponse);

    return NextResponse.json({
      success: true,
      results: openAIResponse.topProfiles.map((profile: any) => ({
        url: profile.url,
        summary: {
          name: profile.name,
          title: profile.title,
          relevanceLevel: profile.relevanceLevel,
          recommendationReason: profile.recommendationReason
        }
      }))
    });

  } catch (error) {
    console.error('Error processing hiring managers:', error);
    return NextResponse.json(
      { error: 'Failed to process hiring managers' },
      { status: 500 }
    );
  }
} 