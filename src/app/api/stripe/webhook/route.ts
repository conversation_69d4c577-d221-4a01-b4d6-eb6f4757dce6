import { createClient } from '@/lib/supabase'
import { headers } from 'next/headers'
import { NextResponse } from 'next/server'
import Stripe from 'stripe'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-03-31.basil',
})

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!

export async function POST(req: Request) {
  try {
    const body = await req.text()
    const signature = headers().get('stripe-signature')!

    let event: Stripe.Event

    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret)
    } catch (err) {
      console.error('Webhook signature verification failed:', err)
      return new NextResponse('Webhook signature verification failed', { status: 400 })
    }

    const supabase = createClient()

    switch (event.type) {
      case 'customer.subscription.created':
      case 'customer.subscription.updated': {
        const subscription = event.data.object as Stripe.Subscription
        const customerId = subscription.customer as string
        const status = subscription.status

        // Get user_id from stripe_customers table
        const { data: customerData, error: customerError } = await supabase
          .from('stripe_customers')
          .select('user_id')
          .eq('customer_id', customerId)
          .single()

        if (customerError) {
          console.error('Error fetching customer:', customerError)
          return new NextResponse('Error fetching customer', { status: 400 })
        }

        // Update user_profiles
        const updates: { plan: string; subscription_end_date: string | null } = {
          plan: status === 'active' ? 'pro' : 'free',
          subscription_end_date: null // Clear end date when subscription is active
        }

        const { error: updateError } = await supabase
          .from('user_profiles')
          .update(updates)
          .eq('id', customerData.user_id)

        if (updateError) {
          console.error('Error updating user profile:', updateError)
          return new NextResponse('Error updating user profile', { status: 400 })
        }
        break
      }

      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription
        const customerId = subscription.customer as string

        // Get user_id from stripe_customers table
        const { data: customerData, error: customerError } = await supabase
          .from('stripe_customers')
          .select('user_id')
          .eq('customer_id', customerId)
          .single()

        if (customerError) {
          console.error('Error fetching customer:', customerError)
          return new NextResponse('Error fetching customer', { status: 400 })
        }

        // Set subscription end date to current period end
        const updates = {
          subscription_end_date: new Date((subscription as any).current_period_end * 1000).toISOString()
        }

        const { error: updateError } = await supabase
          .from('user_profiles')
          .update(updates)
          .eq('id', customerData.user_id)

        if (updateError) {
          console.error('Error updating user profile:', updateError)
          return new NextResponse('Error updating user profile', { status: 400 })
        }
        break
      }
    }

    return new NextResponse('Webhook processed successfully', { status: 200 })
  } catch (err) {
    console.error('Error processing webhook:', err)
    return new NextResponse('Webhook error', { status: 400 })
  }
} 