import { createClient } from '@/lib/supabase'
import { NextResponse } from 'next/server'
import Stripe from 'stripe'
import { headers } from 'next/headers'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-03-31.basil',
})

export async function GET() {
  try {
    // Get the user's session
    const supabase = createClient()
    const { data: { session } } = await supabase.auth.getSession()

    if (!session?.user) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    // Check if user already has a Stripe customer ID
    const { data: customerData } = await supabase
      .from('stripe_customers')
      .select('customer_id')
      .eq('user_id', session.user.id)
      .single()

    let customerId: string

    if (customerData?.customer_id) {
      customerId = customerData.customer_id
    } else {
      // Create a new Stripe customer
      const customer = await stripe.customers.create({
        email: session.user.email!,
        metadata: {
          user_id: session.user.id
        }
      })

      // Save the customer ID
      await supabase
        .from('stripe_customers')
        .insert({
          user_id: session.user.id,
          customer_id: customer.id
        })

      customerId = customer.id
    }

    // Create a Stripe checkout session
    const checkoutSession = await stripe.checkout.sessions.create({
      customer: customerId,
      mode: 'subscription',
      payment_method_types: ['card'],
      line_items: [
        {
          price: process.env.STRIPE_PRICE_ID,
          quantity: 1,
        },
      ],
      success_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/settings?success=true`,
      cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/settings?canceled=true`,
      metadata: {
        user_id: session.user.id
      }
    })

    return NextResponse.json({ url: checkoutSession.url })
  } catch (error) {
    console.error('Error creating checkout session:', error)
    return new NextResponse('Error creating checkout session', { status: 500 })
  }
} 