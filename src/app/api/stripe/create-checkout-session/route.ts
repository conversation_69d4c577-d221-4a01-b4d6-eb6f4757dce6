import { NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createClient } from '@/lib/supabase';
import { cookies } from 'next/headers';

// Initialize Stripe with your secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-03-31.basil', // Use the latest API version
});

// Define the expected request body type
interface CreateCheckoutSessionRequest {
  user_id: string;
}

export async function POST(request: Request) {
  try {
    console.log('🔵 Starting checkout session creation...');
    
    // Get the authorization header from the request
    const supabaseToken = request.headers.get('authorization')?.split('Bearer ')[1];
    
    if (!supabaseToken) {
      console.error('❌ No authorization token found');
      return new Response(JSON.stringify({ error: 'Unauthorized' }), { status: 401 });
    }

    // Initialize Supabase client
    const supabase = createClient();
    
    // Get the user from Supabase using the token
    const { data: { user }, error: userError } = await supabase.auth.getUser(supabaseToken);
    
    if (userError || !user) {
      console.error('❌ Auth error:', userError);
      return new Response(JSON.stringify({ error: 'Unauthorized' }), { status: 401 });
    }
    
    console.log('👤 User found:', user.id);

    // Find or create a customer in Stripe
    let customer;
    const { data: customers } = await stripe.customers.search({
      query: `email:'${user.email}'`,
    });

    if (customers && customers.length > 0) {
      customer = customers[0];
      console.log('Found existing Stripe customer:', customer.id);
    } else {
      customer = await stripe.customers.create({
        email: user.email,
        metadata: {
          user_id: user.id,
        },
      });
      console.log('Created new Stripe customer:', customer.id);
    }

    // Create checkout session
    const session = await stripe.checkout.sessions.create({
      customer: customer.id,
      payment_method_types: ['card'],
      line_items: [
        {
          price: 'price_1RCAZ4P4JVSUtrUMkXuoRTAQ', // Production price - $12.99/month
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${process.env.NEXT_PUBLIC_SITE_URL}/payment/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXT_PUBLIC_SITE_URL}/payment/cancel`,
      metadata: {
        user_id: user.id,
      },
      automatic_tax: {
        enabled: true,
      },
      customer_update: {
        address: 'auto',
      },
      billing_address_collection: 'required',
    });

    if (!session?.url) {
      console.error('❌ No session URL returned');
      return new Response(
        JSON.stringify({ error: 'Failed to create checkout session' }),
        { status: 500 }
      );
    }

    console.log('✅ Checkout session created:', session.id);
    
    return new Response(JSON.stringify({ url: session.url }));
  } catch (error) {
    console.error('❌ Error creating checkout session:', error);
    return new Response(
      JSON.stringify({ error: 'Error creating checkout session' }),
      { status: 500 }
    );
  }
} 