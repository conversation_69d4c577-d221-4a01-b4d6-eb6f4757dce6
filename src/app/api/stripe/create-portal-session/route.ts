import { NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createClient } from '@/lib/supabase';
import { headers } from 'next/headers';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-03-31.basil',
});

// Create or update portal configuration
async function getOrCreatePortalConfig() {
  console.log('Getting portal configuration...');
  
  // First check for existing configurations
  const configurations = await stripe.billingPortal.configurations.list();
  if (configurations.data.length > 0) {
    console.log('Using existing portal configuration:', configurations.data[0].id);
    return configurations.data[0].id;
  }
  
  // If no configuration exists, create one
  console.log('No configuration found, creating new one...');
  
  // Get active products and prices
  const products = await stripe.products.list({ active: true });
  const prices = await stripe.prices.list({ active: true });
  
  console.log('Available products:', products.data.map(p => ({ id: p.id, name: p.name })));
  console.log('Available prices:', prices.data.map(p => ({ id: p.id, amount: p.unit_amount })));

  const configuration = await stripe.billingPortal.configurations.create({
    business_profile: {
      headline: 'Manage your subscription',
      privacy_policy_url: `${process.env.NEXT_PUBLIC_SITE_URL}/privacy`,
      terms_of_service_url: `${process.env.NEXT_PUBLIC_SITE_URL}/terms`,
    },
    features: {
      subscription_update: {
        enabled: true,
        products: products.data.map(p => ({ product: p.id, prices: prices.data.map(price => price.id) })),
        default_allowed_updates: ['price'],
      },
      subscription_cancel: {
        enabled: true,
        mode: 'immediately',
        cancellation_reason: {
          enabled: true,
          options: ['too_expensive', 'missing_features', 'other'],
        },
      },
      payment_method_update: { enabled: true },
      customer_update: {
        enabled: true,
        allowed_updates: ['email', 'address', 'shipping', 'phone', 'tax_id'],
      },
      invoice_history: { enabled: true },
    },
  });

  console.log('Created new portal configuration:', configuration.id);
  return configuration.id;
}

export async function POST() {
  try {
    // Get the authorization header
    const headersList = headers();
    const authHeader = headersList.get('Authorization');

    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Missing or invalid authorization header' },
        { status: 401 }
      );
    }

    // Extract the token
    const token = authHeader.split(' ')[1];
    
    // Create a Supabase client
    const supabase = createClient();
    
    // Verify the user's session using the token
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      console.error('Auth error:', authError);
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    console.log('Authenticated user:', user.id);

    // Get the user's email directly from auth
    const userEmail = user.email;
    
    if (!userEmail) {
      console.error('No email found in auth user data');
      return NextResponse.json(
        { error: 'User email not found' },
        { status: 400 }
      );
    }

    console.log('Creating portal session for user:', user.id);

    // Find or create a customer in Stripe
    let customer;
    const { data: customers } = await stripe.customers.search({
      query: `email:'${userEmail}'`,
    });

    if (customers && customers.length > 0) {
      customer = customers[0];
      console.log('Found existing Stripe customer:', customer.id);
      
      // Get customer's subscriptions
      const subscriptions = await stripe.subscriptions.list({
        customer: customer.id,
        status: 'active',
      });
      
      console.log('Customer subscriptions:', JSON.stringify(subscriptions.data, null, 2));
      
      // Ensure customer has a default payment method
      const paymentMethods = await stripe.paymentMethods.list({
        customer: customer.id,
        type: 'card',
      });
      
      if (paymentMethods.data.length > 0) {
        // If customer doesn't have a default payment method, set the first one as default
        if (!customer.default_source && !customer.invoice_settings?.default_payment_method) {
          await stripe.customers.update(customer.id, {
            invoice_settings: {
              default_payment_method: paymentMethods.data[0].id,
            },
          });
          console.log('Updated customer with default payment method');
        }
      }
      
    } else {
      customer = await stripe.customers.create({
        email: userEmail,
        metadata: {
          user_id: user.id,
        },
      });
      console.log('Created new Stripe customer:', customer.id);
    }

    // Create a Stripe Customer Portal session
    const configId = await getOrCreatePortalConfig();
    const session = await stripe.billingPortal.sessions.create({
      customer: customer.id,
      return_url: `${process.env.NEXT_PUBLIC_SITE_URL}/dashboard/settings`,
      configuration: configId
    });

    console.log('Created portal session:', session.url);
    return NextResponse.json({ url: session.url });
  } catch (error) {
    console.error('Error creating portal session:', error);
    return NextResponse.json(
      { error: 'Could not create portal session' },
      { status: 500 }
    );
  }
} 