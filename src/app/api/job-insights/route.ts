import { NextResponse } from 'next/server';
import OpenAI from 'openai';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

export async function POST(req: Request) {
  try {
    const { jobTitle, companyName, description, yearsOfExperience, department } = await req.json();

    const prompt = `Given a job posting for a ${jobTitle} position at ${companyName} in the ${department} department${yearsOfExperience ? ` requiring ${yearsOfExperience}+ years of experience` : ''}, please provide:

1. A warm, conversational LinkedIn message to the hiring manager that follows this structure:
   - Start with a friendly greeting
   - Mention something specific about the role or team that caught your attention
   - End with a clear call to action asking for a conversation about the role
   
Keep it under 200 characters. The tone should be:
- Warm and friendly, but professional
- Enthusiastic but not overeager
- Direct and confident, but not pushy
- Natural, like how you'd message a colleague

Example structure (but make it unique):
"Hi [Name], [specific observation about role/team]. Would love to chat about [specific aspect] if you have a moment!"

2. Interview process insights including:
   - A key focus area for the role (1-2 sentences)
   - Top 5 question areas candidates should prepare for
   - A specific suggestion for standing out in the process
   - A strategic interview tip

Job Description: ${description || 'Not provided'}

Format the response as JSON with the following structure:
{
  "linkedinMessage": "string (under 200 characters)",
  "interviewProcess": {
    "keyFocus": "string",
    "topQuestionAreas": ["string", "string", "string", "string", "string"],
    "standoutSuggestion": "string",
    "interviewTip": "string"
  }
}`;

    const completion = await openai.chat.completions.create({
      messages: [
        {
          role: "system",
          content: "You are an expert career coach who excels at writing engaging, natural-sounding LinkedIn messages that get responses and breaking down complex interview processes into clear, actionable steps. You understand how to present information in a way that's both professional and easily digestible."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      model: "gpt-4o-mini",
      response_format: { type: "json_object" }
    });

    const response = completion.choices[0].message.content;
    
    if (!response) {
      throw new Error('No response from OpenAI');
    }

    return NextResponse.json(JSON.parse(response));
  } catch (error) {
    console.error('Error generating job insights:', error);
    return NextResponse.json(
      { error: 'Failed to generate job insights' },
      { status: 500 }
    );
  }
} 