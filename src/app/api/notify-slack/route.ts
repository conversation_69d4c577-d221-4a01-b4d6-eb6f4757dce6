import { NextResponse } from 'next/server'

export async function POST(request: Request) {
  if (!process.env.SLACK_WEBHOOK_URL) {
    return NextResponse.json({ error: 'Slack webhook not configured' }, { status: 500 })
  }

  try {
    const { message } = await request.json()
    
    const response = await fetch(process.env.SLACK_WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ text: message }),
    })

    if (!response.ok) {
      throw new Error('Failed to send to Slack')
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Slack notification error:', error)
    return NextResponse.json({ error: 'Failed to send notification' }, { status: 500 })
  }
}