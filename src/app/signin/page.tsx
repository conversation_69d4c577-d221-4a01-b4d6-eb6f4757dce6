'use client'

import { useState, useEffect } from 'react'
import { useAuth, useAuthRedirect } from '@/hooks'
import Link from 'next/link'
import { Input } from "@/components/ui/input"
import { FcGoogle } from 'react-icons/fc'
import { createClient } from '@/lib/supabase'
import { toast } from 'react-hot-toast'

export default function SignIn() {
  const [email, setEmail] = useState('')
  const [showSuccessMessage, setShowSuccessMessage] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const { signInWithEmail, loading, error } = useAuth()
  const { user } = useAuthRedirect()

  // Detect if user is on mobile device
  useEffect(() => {
    const checkIfMobile = () => {
      const userAgent = window.navigator.userAgent.toLowerCase()
      const mobileRegex = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i
      setIsMobile(mobileRegex.test(userAgent) || window.innerWidth <= 768)
    }

    checkIfMobile()
    window.addEventListener('resize', checkIfMobile)
    
    return () => window.removeEventListener('resize', checkIfMobile)
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    const { error: signInError } = await signInWithEmail(email)
    if (!signInError) {
      setShowSuccessMessage(true)
      setEmail('')
      toast.success("We'll send you a magic link to sign in!")
    }
  }

  const handleGoogleSignIn = async () => {
    const supabase = createClient()
    await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/auth/callback`
      }
    })
  }

  if (user) return null

  return (
    <main className="flex min-h-screen flex-col items-center justify-center bg-gradient-to-b from-gray-50 to-transparent">
      <div className="absolute top-8 left-8">
        <Link 
          href="/" 
          className="text-2xl font-bold text-gray-900 hover:text-[#4338CA] transition-colors"
        >
          Awaloon
        </Link>
      </div>
      
      <div className="w-full max-w-md bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
        <h1 className="text-3xl font-bold mb-6 text-gray-900 text-center">Welcome Back</h1>
        {error && (
          <div className="mb-4 p-4 text-sm text-red-600 bg-red-50 rounded-lg">
            {error}
          </div>
        )}
        
        {showSuccessMessage && !isMobile && (
          <div className="mb-4 p-4 text-sm text-green-600 bg-green-50 rounded-lg">
            {"We'll send you a magic link to sign in!"}
            <button 
              onClick={() => setShowSuccessMessage(false)}
              className="ml-2 underline hover:no-underline"
            >
              Try again?
            </button>
          </div>
        )}
        
        {!showSuccessMessage && (
          <>
            <button
              onClick={handleGoogleSignIn}
              className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-white hover:bg-gray-50 text-gray-900 rounded-lg transition-all hover:-translate-y-0.5 shadow-sm border border-gray-200 mb-6"
              disabled={loading}
            >
              <FcGoogle className="text-xl" />
              Continue with Google
            </button>

            {!isMobile && (
              <>
                <div className="relative mb-6">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-200"></div>
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-white text-gray-500">Or continue with email</span>
                  </div>
                </div>

                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <Input
                      type="email"
                      placeholder="Email address"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                      className="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                      disabled={loading}
                    />
                  </div>
                  
                  <button
                    type="submit"
                    disabled={loading}
                    className="w-full px-4 py-2 bg-[#4F46E5] hover:bg-[#4338CA] text-white rounded-lg transition-all hover:-translate-y-0.5 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {loading ? "Signing in..." : "Sign in with Email"}
                  </button>
                </form>
              </>
            )}
          </>
        )}

        <p className="mt-6 text-center text-gray-600">
          Don&apos;t have an account?{" "}
          <Link 
            href="/signup" 
            className="text-[#4F46E5] hover:text-[#4338CA] font-medium transition-colors"
          >
            Sign up
          </Link>
        </p>
      </div>
    </main>
  )
}