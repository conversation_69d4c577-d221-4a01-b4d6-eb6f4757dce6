'use client'

import { useEffect, useState, useRef } from 'react'
import { ProtectedRoute } from '@/components/ui/ProtectedRoute'
import { useAuth } from '@/contexts/AuthContext'
import { createClient } from '@/lib/supabase'
import { FiLogOut } from 'react-icons/fi'
import { FilterProvider, useFilter } from '@/contexts/FilterContext'
import { toast } from 'react-hot-toast'
import { JobRole, roleDisplayNames, formatRoleForDisplay } from '@/lib/roles'
import { DashboardLayout } from '@/components/ui/DashboardLayout'
import { AlertProgressBar } from '@/components/ui/AlertProgressBar'
import { Sparkles } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { inject } from '@vercel/analytics'
import { SubscribeButton } from '@/components/SubscribeButton'
import { useUserProfile } from '@/contexts/UserProfileContext'
import { FeatureGate } from '@/components/ui/FeatureGate'
import { CompanySelectionSection } from '@/components/ui/CompanySelectionSection'
import { TrackedJobsSection } from '@/components/ui/TrackedJobsSection'

interface UserProfile {
  id: string
  filter_companies: string[]
  filter_role: string[] | null
  filter_experience: string | null
  filter_location: string | null
  filter_alert_frequency: string | null
  added_bundles?: string[] // Array of bundle titles that have been added
}

interface AlertsTableProps {
  companies: string[];
  role: string | null;
  onDeleteCompany: (companyToDelete: string) => Promise<void>;
  experience?: string | null;
  location?: string | null;
}

function DashboardContent({ 
  profile, 
  loading, 
  handleDeleteCompany, 
  handleAlertCreated, 
  signOut,
  user,
  currentRole,
  handleRoleChange,
  setCurrentRole,
  handleBundleAdded,
  timeframeMessage,
  setTimeframeMessage,
  requestedCompanies,
  setRequestedCompanies
}: { 
  profile: UserProfile | null;
  loading: boolean;
  handleDeleteCompany: (company: string) => Promise<void>;
  handleAlertCreated: (data: { companies: string[]; role: string[]; error?: 'already_tracking' | 'update_failed' }) => void;
  signOut: () => void;
  user: any;
  currentRole: string[];
  handleRoleChange: (newRole: string[]) => Promise<void>;
  setCurrentRole: React.Dispatch<React.SetStateAction<string[]>>;
  handleBundleAdded: (bundleTitle: string) => Promise<void>;
  timeframeMessage: string;
  setTimeframeMessage: React.Dispatch<React.SetStateAction<string>>;
  requestedCompanies: Set<string>;
  setRequestedCompanies: React.Dispatch<React.SetStateAction<Set<string>>>;
}) {
  const { setSelectedCompanies } = useFilter();
  const quickAddRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const isAuthenticated = !!user;
  const { isProUser } = useUserProfile();

  const scrollToQuickAdd = () => {
    quickAddRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleBundleSelect = async (companies: string[], role: string[], bundleTitle?: string) => {
    if (!profile || !user) {
      toast.error('Please sign in to create alerts');
      return;
    }
    
    try {
      // Get new companies that aren't already being tracked
      const existingCompanies = profile.filter_companies || [];
      const newCompanies = companies.filter(company => !existingCompanies.includes(company));
      
      if (newCompanies.length === 0) {
        toast.error("You're already tracking these companies");
        return;
      }

      // Merge existing companies with new ones
      const mergedCompanies = Array.from(new Set([...existingCompanies, ...newCompanies]));
      
      // Normalize the role before saving
      const normalizedRole = normalizeRole(role);
      
      // Save to Supabase
      const supabase = createClient();
      const { data, error: updateError } = await supabase
        .from('user_profiles')
        .update({
          filter_companies: mergedCompanies,
          filter_role: normalizedRole
        })
        .eq('id', user.id)
        .select();

      if (updateError) {
        console.error('Error updating profile:', updateError);
        handleAlertCreated({ 
          companies: mergedCompanies, 
          role: normalizedRole,
          error: 'update_failed' 
        });
        return;
      }

      // Update local state through handleAlertCreated
      handleAlertCreated({ 
        companies: mergedCompanies, 
        role: normalizedRole
      });
      
      // Update current role state
      setCurrentRole(normalizedRole);

      // If we have a bundle title, mark it as added
      if (bundleTitle) {
        await handleBundleAdded(bundleTitle);
      }
    } catch (error) {
      console.error('Error in handleBundleSelect:', error);
      handleAlertCreated({ 
        companies, 
        role: normalizeRole(role),
        error: 'update_failed' 
      });
    }
  };

  const handleButtonClick = () => {
    if (!isAuthenticated) {
      router.push('/signup');
    }
  };

  useEffect(() => {
    inject(); // Initialize analytics
  }, []);

  return (
    <div className="p-4 sm:p-8 flex-grow">
      <div className="max-w-5xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-lg sm:text-xl font-bold text-gray-900 md:ml-0 ml-12">Set job alerts</h1>
          <button
            onClick={(e) => {
              e.preventDefault();
              signOut();
            }}
            className="flex items-center justify-center gap-2 px-4 py-2 text-gray-700 hover:text-gray-900 
              hover:bg-gray-50 rounded-lg transition-all border border-gray-200"
          >
            <FiLogOut className="text-lg" />
            <span className="hidden sm:inline">Sign Out</span>
          </button>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-gray-900"></div>
          </div>
        ) : profile ? (
          <div className="space-y-6 sm:space-y-12">
            <AlertProgressBar 
              alertCount={profile.filter_companies.length}
              onQuickAddClick={scrollToQuickAdd}
            />

            <div className="space-y-6">
              <div ref={quickAddRef} className="bg-white rounded-xl border border-gray-200 p-6">
                <CompanySelectionSection
                  currentRole={normalizeRole(profile.filter_role)}
                  onRoleChange={handleRoleChange}
                  onSelectBundle={handleBundleSelect}
                  onAlertCreated={handleAlertCreated}
                  addedBundles={profile.added_bundles || []}
                  initialCompanies={profile.filter_companies || []}
                  isAuthenticated={true}
                />
              </div>

              {/* Tracked Jobs Section */}
              <TrackedJobsSection />
            </div>

            {/* Alert Signup */}
            {!isAuthenticated && (
              <div className="max-w-xl mx-auto">
                <div className="flex flex-col sm:flex-row items-stretch">
                  <div className="flex items-center gap-2 p-4 bg-gray-50 rounded-lg sm:rounded-r-none border border-gray-200 sm:border-r-0 flex-grow mb-3 sm:mb-0">
                    <Sparkles className="w-4 h-4 flex-shrink-0 text-[#4F46E5]" />
                    <span className="text-sm whitespace-nowrap">
                      <span className="font-medium text-gray-900">Alert:</span>
                      <span className="text-gray-600 ml-1">{getDisplayRole(currentRole[0])} at {profile.filter_companies.join(', ')}</span>
                    </span>
                  </div>

                  <button
                    onClick={handleButtonClick}
                    className="w-full sm:w-auto bg-[#4F46E5] hover:bg-[#4338CA] text-white px-6 sm:px-8 py-3 rounded-lg sm:rounded-l-none font-medium transition-colors flex-shrink-0 sm:min-w-[180px]"
                  >
                    Sign up to get notified
                  </button>
                </div>
                <p className="text-xs text-center mt-2 sm:hidden text-gray-500">
                  We&apos;ll only notify you when relevant positions become available
                </p>
              </div>
            )}
          </div>
        ) : (
          <p className="text-lg sm:text-xl text-gray-600">No profile data found. Please complete your profile.</p>
        )}
      </div>

      <footer className="w-full py-8 mt-16 border-t border-gray-100">
        <div className="max-w-5xl mx-auto px-4 sm:px-8 text-center">
          <p className="text-sm text-gray-500">
            Logos provided by{' '}
            <a 
              href="https://logo.dev" 
              target="_blank" 
              rel="noopener"
              className="text-[#4F46E5] hover:underline"
            >
              Logo.dev
            </a>
          </p>
        </div>
      </footer>
    </div>
  );
}

const normalizeRole = (role: string[] | null): string[] => {
  if (!role || role.length === 0) return ['*'];
  
  // Handle array with single empty string
  if (role.length === 1 && role[0] === '') return ['*'];
  
  // If the role is already in JobRole enum format (e.g. "PRODUCT_MANAGER"), keep it
  if (role[0] in JobRole) {
    console.log('Role is already in JobRole enum format:', role[0]);
    return role;
  }
  
  // If the role is in display format (e.g. "Product Manager"), convert it to enum
  const enumEntry = Object.entries(roleDisplayNames).find(([_, displayName]) => displayName === role[0]);
  if (enumEntry) {
    console.log('Converting display format to enum:', { from: role[0], to: enumEntry[0] });
    return [enumEntry[0]];
  }
  
  // If we get here, try to parse the role if it's a stringified array
  try {
    if (typeof role[0] === 'string' && role[0].startsWith('[')) {
      const parsed = JSON.parse(role[0]);
      if (Array.isArray(parsed) && parsed[0] in JobRole) {
        console.log('Parsed stringified array to role:', parsed);
        return parsed;
      }
    }
  } catch (e) {
    console.error('Error parsing stringified role:', e);
  }
  
  console.log('Could not normalize role, using original:', role);
  return role;
};

export default function Dashboard() {
  const { user, signOut } = useAuth()
  const router = useRouter()
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [currentRole, setCurrentRole] = useState<string[]>(['*'])
  const [timeframeMessage, setTimeframeMessage] = useState("We&apos;re displaying jobs added in the last 7 days.")
  const [requestedCompanies, setRequestedCompanies] = useState<Set<string>>(new Set())
  const isAuthenticated = !!user

  // Load initial profile data
  useEffect(() => {
    async function fetchUserProfile() {
      if (user) {
        const supabase = createClient()
        const { data, error } = await supabase
          .from('user_profiles')
          .select('*')
          .eq('id', user.id)
          .single()

        if (error) {
          console.error('Error fetching user profile:', error)
        } else {
          console.log('Fetched profile data:', data);
          
          // Parse the role from JSON string if needed
          let role;
          try {
            role = data.filter_role ? JSON.parse(data.filter_role) : ['*'];
            console.log('Parsed role from DB:', role);
          } catch (e) {
            console.error('Error parsing role:', e);
            role = ['*'];
          }
          
          // Ensure role is in the correct format
          const normalizedRole = role.map((r: string) => {
            // If it's already a valid JobRole enum value, keep it
            if (r in JobRole) {
              console.log('Found valid JobRole:', r);
              return r;
            }
            // If it's a display name, convert it back to enum
            const enumEntry = Object.entries(roleDisplayNames).find(([_, display]) => display === r);
            console.log('Converting display name to enum:', { r, enumEntry });
            return enumEntry ? enumEntry[0] : '*';
          });
          
          console.log('Setting normalized role:', normalizedRole);
          setCurrentRole(normalizedRole);
          setProfile({
            ...data,
            filter_role: normalizedRole
          });
        }
        setLoading(false)
      }
    }

    fetchUserProfile()
  }, [user])

  // Handle role changes
  const handleRoleChange = async (newRole: string[]) => {
    console.log('Dashboard handleRoleChange:', newRole);
    
    if (!user || !profile) return;

    try {
      const supabase = createClient();
      const { error } = await supabase
        .from('user_profiles')
        .update({ filter_role: newRole })
        .eq('id', user.id);

      if (error) {
        console.error('Error updating role:', error);
        return;
      }

      // Update local state
      setCurrentRole(newRole);
      setProfile(prev => prev ? {
        ...prev,
        filter_role: newRole
      } : null);

    } catch (error) {
      console.error('Error in handleRoleChange:', error);
    }
  };

  useEffect(() => {
    if (profile) {
      console.log('Profile state updated:', {
        companies: profile.filter_companies,
        role: profile.filter_role
      });
    }
  }, [profile]);

  const handleDeleteCompany = async (companyToDelete: string) => {
    if (!profile) return

    const updatedCompanies = profile.filter_companies.filter(
      company => company !== companyToDelete
    )

    const supabase = createClient()
    const { error } = await supabase
      .from('user_profiles')
      .update({ filter_companies: updatedCompanies })
      .eq('id', user?.id)

    if (!error) {
      setProfile({
        ...profile,
        filter_companies: updatedCompanies
      })
    }
  }

  const handleAlertCreated = ({ companies, role, error }: { 
    companies: string[], 
    role: string[], 
    error?: 'already_tracking' | 'update_failed' 
  }) => {
    console.log('handleAlertCreated called with:', { companies, role, error });
    
    if (profile) {
      if (error) {
        if (error === 'already_tracking') {
          toast.error("You're already tracking these companies");
        } else if (error === 'update_failed') {
          toast.error('Failed to create alert');
        }
        return;
      }

      // Get new companies that weren't previously tracked
      const newCompanies = companies.filter(company => !profile.filter_companies.includes(company));
      
      // Normalize the role
      const normalizedRole = normalizeRole(role);
      console.log('handleAlertCreated - Role normalization:', {
        originalRole: role,
        normalizedRole
      });
      
      if (newCompanies.length > 0) {
        const roleText = normalizedRole.includes('*') ? 'all roles' : normalizedRole.map(r => formatRoleForDisplay(r)).join(', ');
        toast.success(`Successfully created alerts for ${roleText} at ${newCompanies.length} ${newCompanies.length === 1 ? 'company' : 'companies'}`);
      }

      console.log('Updating profile state with:', { companies, role: normalizedRole });
      
      // Update local state
      setProfile({
        ...profile,
        filter_companies: companies,
        filter_role: normalizedRole
      });

      // Update message based on results
      if (companies.length === 0) {
        const roleDisplay = getDisplayRole(normalizedRole[0]);
        setTimeframeMessage(`We're actively searching for ${roleDisplay} positions at these companies.`);
      } else if (newCompanies.length > 0) {
        setTimeframeMessage(`We're displaying jobs added in the last ${newCompanies.length} days.`);
      } else {
        setTimeframeMessage('Showing available job listings.');
      }
    }
  }

  // Handle adding a bundle to the user's added_bundles list
  const handleBundleAdded = async (bundleTitle: string) => {
    if (!user || !profile) return;

    try {
      const supabase = createClient();
      const updatedBundles = [...(profile.added_bundles || []), bundleTitle];
      
      const { error } = await supabase
        .from('user_profiles')
        .update({ added_bundles: updatedBundles })
        .eq('id', user.id);

      if (error) {
        console.error('Error updating added bundles:', error);
        return;
      }

      // Update local state
      setProfile(prev => prev ? {
        ...prev,
        added_bundles: updatedBundles
      } : null);
    } catch (error) {
      console.error('Error in handleBundleAdded:', error);
    }
  };

  return (
    <ProtectedRoute>
      <FilterProvider>
        <DashboardLayout>
          <DashboardContent
            profile={profile}
            loading={loading}
            handleDeleteCompany={handleDeleteCompany}
            handleAlertCreated={handleAlertCreated}
            signOut={signOut}
            user={user}
            currentRole={currentRole}
            handleRoleChange={handleRoleChange}
            setCurrentRole={setCurrentRole}
            handleBundleAdded={handleBundleAdded}
            timeframeMessage={timeframeMessage}
            setTimeframeMessage={setTimeframeMessage}
            requestedCompanies={requestedCompanies}
            setRequestedCompanies={setRequestedCompanies}
          />
        </DashboardLayout>
      </FilterProvider>
    </ProtectedRoute>
  )
}

// Helper function to format role for display
const getDisplayRole = (roleValue: string) => {
  if (roleValue === '*') return 'All Roles';
  // If it's an enum value, convert to display name
  if (roleValue in JobRole) {
    return roleDisplayNames[roleValue as JobRole];
  }
  // If it's already a display name, use it as is
  return roleValue;
};
