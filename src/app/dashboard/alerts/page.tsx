'use client'

import { useState, useEffect } from 'react'
import { DashboardLayout } from '@/components/ui/DashboardLayout'
import { ProtectedRoute } from '@/components/ui/ProtectedRoute'
import { AlertsList } from '@/components/ui/AlertsList'
import { useAuth } from '@/contexts/AuthContext'
import { createClient } from '@/lib/supabase'
import { FiTrash2 } from 'react-icons/fi'
import { formatRoleForDisplay } from '@/lib/roles'

interface UserProfile {
  id: string
  filter_companies: string[]
  filter_role: string[] | null
  filter_experience: string | null
  filter_location: string | null
  filter_alert_frequency: string | null
}

export default function AlertsPage() {
  const { user } = useAuth()
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [currentRole, setCurrentRole] = useState<string[]>(['*'])

  // Load user profile data
  useEffect(() => {
    async function fetchUserProfile() {
      if (user) {
        const supabase = createClient()
        const { data, error } = await supabase
          .from('user_profiles')
          .select('*')
          .eq('id', user.id)
          .single()

        if (error) {
          console.error('Error fetching user profile:', error)
        } else {
          setProfile(data)
          setCurrentRole(data.filter_role || ['*'])
        }
        setLoading(false)
      }
    }

    fetchUserProfile()
  }, [user])

  const handleDeleteCompany = async (companyToDelete: string) => {
    if (!profile || !user) return

    const updatedCompanies = profile.filter_companies.filter(
      company => company !== companyToDelete
    )

    const supabase = createClient()
    const { error } = await supabase
      .from('user_profiles')
      .update({ filter_companies: updatedCompanies })
      .eq('id', user.id)

    if (!error) {
      setProfile({
        ...profile,
        filter_companies: updatedCompanies
      })
    }
  }

  const handleDeleteAllAlerts = async () => {
    if (!profile || !user) return

    const supabase = createClient()
    const { error } = await supabase
      .from('user_profiles')
      .update({ filter_companies: [] })
      .eq('id', user.id)

    if (!error) {
      setProfile({
        ...profile,
        filter_companies: []
      })
    }
  }

  return (
    <ProtectedRoute>
      <DashboardLayout>
        <div className="p-4 sm:p-8">
          <div className="max-w-5xl mx-auto space-y-6">
            {/* Header Section */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div>
                <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">My Alerts</h1>
                {profile && profile.filter_companies.length > 0 && (
                  <p className="mt-1 text-sm text-gray-600">
                    You{'\''}re tracking {profile.filter_companies.length} {profile.filter_companies.length === 1 ? 'company' : 'companies'} for{' '}
                    {currentRole.includes('*') ? 'all roles' : formatRoleForDisplay(currentRole[0])}
                  </p>
                )}
              </div>
              
              {profile && profile.filter_companies.length > 0 && (
                <button
                  onClick={handleDeleteAllAlerts}
                  className="inline-flex items-center px-4 py-2 text-sm font-medium text-red-600 bg-red-50 hover:bg-red-100 rounded-lg transition-colors"
                >
                  <FiTrash2 className="mr-2" />
                  Delete All Alerts
                </button>
              )}
            </div>
            
            {loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-gray-900"></div>
              </div>
            ) : profile ? (
              <AlertsList 
                companies={profile.filter_companies}
                role={currentRole}
                onDeleteCompany={handleDeleteCompany}
              />
            ) : (
              <p className="text-lg text-gray-600">
                No alerts found. Create some alerts to get started!
              </p>
            )}
          </div>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  )
} 