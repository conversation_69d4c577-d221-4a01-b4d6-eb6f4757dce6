'use client'

import { DashboardLayout } from '@/components/ui/DashboardLayout'
import { ProtectedRoute } from '@/components/ui/ProtectedRoute'
import { useAuth } from '@/contexts/AuthContext'
import { useUserProfile } from '@/contexts/UserProfileContext'
import { FiMail } from 'react-icons/fi'
import { Sparkles, Calendar, Loader2 } from 'lucide-react'
import { format } from 'date-fns'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import Link from 'next/link'
import { SubscribeButton } from '@/components/SubscribeButton'

export default function SettingsPage() {
  const { user } = useAuth()
  const { profile, loading, isProUser } = useUserProfile()

  const getSubscriptionMessage = () => {
    if (!profile) return null
    
    if (profile.subscription_end_date) {
      return (
        <div className="mt-2 flex items-center text-sm text-gray-500">
          <Calendar className="w-4 h-4 mr-1" />
          Access until {format(new Date(profile.subscription_end_date), 'MMMM d, yyyy')}
        </div>
      )
    }

    if (isProUser) {
      return (
        <div className="mt-2 flex items-center text-sm text-green-600">
          <Sparkles className="w-4 h-4 mr-1" />
          Active Pro Subscription
        </div>
      )
    }

    return null
  }

  const handleManageSubscription = async () => {
    try {
      const response = await fetch('/api/stripe/portal', {
        method: 'POST',
      })
      const { url } = await response.json()
      window.location.href = url
    } catch (error) {
      console.error('Error accessing billing portal:', error)
    }
  }

  return (
    <ProtectedRoute>
      <DashboardLayout>
        <div className="max-w-4xl mx-auto p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-8">Settings</h1>

          <div className="space-y-6">
            {/* Account Information */}
            <Card>
              <div className="p-6">
                <h2 className="text-base font-semibold text-gray-900 mb-4">
                  Account Information
                </h2>
                {loading ? (
                  <div className="animate-pulse space-y-3">
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm text-gray-600">Email</p>
                      <div className="flex items-center mt-1">
                        <FiMail className="text-gray-400 mr-2" />
                        <p className="text-gray-900">{user?.email}</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </Card>

            {/* Plan Management */}
            <Card>
              <div className="p-6">
                <h2 className="text-base font-semibold text-gray-900 mb-4">
                  Plan Management
                </h2>
                {loading ? (
                  <div className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                  </div>
                ) : profile ? (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600">Current Plan</p>
                        <p className="text-lg font-medium text-gray-900 capitalize">
                          {profile.plan}
                        </p>
                        {getSubscriptionMessage()}
                      </div>
                      {isProUser ? (
                        <Button
                          onClick={handleManageSubscription}
                          variant="outline"
                          className="text-gray-700 hover:text-gray-900"
                        >
                          Manage Subscription
                        </Button>
                      ) : user?.id ? (
                        <SubscribeButton
                          userId={user.id}
                          className="inline-flex items-center justify-center px-4 py-2 bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 text-white rounded-lg transition-colors"
                        >
                          Upgrade to Pro
                        </SubscribeButton>
                      ) : null}
                    </div>

                    {!isProUser && (
                      <div className="mt-6 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg p-4">
                        <h3 className="text-sm font-medium text-gray-900 flex items-center gap-2 mb-2">
                          <Sparkles className="w-4 h-4 text-indigo-500" />
                          Pro Plan Benefits
                        </h3>
                        <ul className="text-sm text-gray-600 space-y-2">
                          <li>• Intelligent Outreach Kit - Personalized messages for hiring managers</li>
                          <li>• Smart Networking Assistant - Find and connect with the right people</li>
                          <li>• Advanced Job Search Insights</li>
                          <li>• More Pro features coming soon!</li>
                        </ul>
                      </div>
                    )}
                  </div>
                ) : (
                  <p className="text-gray-600">Unable to load plan information</p>
                )}
              </div>
            </Card>
          </div>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  )
} 