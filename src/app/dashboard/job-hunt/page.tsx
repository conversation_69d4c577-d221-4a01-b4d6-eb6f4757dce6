'use client'

import { useEffect, useState } from 'react'
import { createClient } from '@/lib/supabase'
import { useAuth } from '@/contexts/AuthContext'
import { MapPin, Sparkles, Users, Bell } from 'lucide-react'
import Link from 'next/link'
import { formatDistanceToNow } from 'date-fns'
import { CompanyLogo } from '@/components/ui/CompanyLogo'
import { formatRoleForDisplay } from '@/lib/roles'
import { DashboardLayout } from '@/components/ui/DashboardLayout'
import { ProtectedRoute } from '@/components/ui/ProtectedRoute'
import { JobAIInsights } from '@/components/ui/JobAIInsights'
import { KeyContacts } from '@/components/ui/KeyContacts'
import { useRateLimit } from '@/hooks/useRateLimit'
import { toast } from 'react-hot-toast'
import { useUserProfile } from '@/contexts/UserProfileContext'
import { UpgradeToProModal } from '@/components/ui/UpgradeToProModal'
import { But<PERSON> } from '@/components/ui/button'
import { UnifiedJobCard } from '@/components/ui/UnifiedJobCard'

// Add the formatLocations helper function
const formatLocations = (location: string | null | undefined): string => {
  if (!location || location.trim() === '') return 'Not available';
  
  try {
    // Check if it's a JSON string array
    const locations = JSON.parse(location);
    if (Array.isArray(locations)) {
      // Filter out any empty strings and join with bullets
      const validLocations = locations.filter(loc => loc && loc.trim() !== '').map(loc => 
        loc.replace(/["\[\]]/g, '').trim()
      );
      return validLocations.length > 0 ? validLocations.join(' • ') : 'Not available';
    }
    // If it's a single location string, remove quotes and brackets
    return location.replace(/["\[\]]/g, '').trim() || 'Not available';
  } catch {
    // If it's not JSON, return the original string if it's not empty
    return location.replace(/["\[\]]/g, '').trim() || 'Not available';
  }
};

// Helper to always show "x time ago" and never "in x minutes"
function formatTimeAgo(dateString: string) {
  // Validate the input first
  if (!dateString || typeof dateString !== 'string' || dateString.trim() === '') {
    return 'Unknown time';
  }
  
  // If the string doesn't end with 'Z', add it to force UTC
  let safeString = dateString.trim();
  if (!safeString.endsWith('Z')) {
    safeString += 'Z';
  }
  
  const date = new Date(safeString);
  
  // Check if the date is valid
  if (isNaN(date.getTime())) {
    return 'Unknown time';
  }
  
  const now = new Date();
  // If the date is in the future, use now instead
  const safeDate = date > now ? now : date;
  return formatDistanceToNow(safeDate, { addSuffix: true });
}

// Database table interfaces
interface DbUserJobLink {
  id: number
  user_id: string // UUID but represented as string in TypeScript
  job_id: number
  date_sent: string // Timestamp represented as string in JSON
}

interface DbJobListing {
  id: number
  title: string
  description?: string
  url?: string
  location?: string
  salary_range?: any // jsonb type
  created_at?: string
  updated_at?: string
  company_name: string
  category_dept?: string
  seniority_level?: string
  management_level?: string
  is_manager?: boolean
  years_of_experience?: number
  related_skills?: string[]
  profitable?: boolean
  funding_round?: string
  location_type?: string
  date_posted?: string
}

interface DbCompany {
  logo_url: string | null
}

// Interface for the joined data from Supabase
interface SupabaseResponse {
  job_id: number
  date_sent: string
  job_listings: {
    id: number
    title: string
    company_name: string
    location: string | null
    location_type: string | null
    url: string | null
    date_posted: string | null
    category_dept: string | null
    description: string | null
    years_of_experience: number | null
    companies: {
      logo_url: string | null
    } | null
  }
}

// Interface for our component's state
interface JobMatch {
  job_id: number
  date_sent: string
  job_listings: {
    id: number
    title: string
    company_name: string
    location: string | null
    location_type: string | null
    url: string | null
    date_posted: string | null
    category_dept: string | null
    description: string | null
    years_of_experience: number | null
    companies: {
      logo_url: string | null
    } | null
  }
}

export default function JobHunt() {
  const [matchedJobs, setMatchedJobs] = useState<JobMatch[]>([])
  const [selectedJobId, setSelectedJobId] = useState<number | null>(null)
  const [selectedHiringManagersJobId, setSelectedHiringManagersJobId] = useState<number | null>(null)
  const [loading, setLoading] = useState(true)
  const { user } = useAuth()
  const { remainingCalls, isLimited, incrementUsage } = useRateLimit()
  const { isProUser } = useUserProfile()
  const [upgradeModalOpen, setUpgradeModalOpen] = useState(false)
  const [upgradeFeature, setUpgradeFeature] = useState<'insights' | 'contacts' | null>(null)

  useEffect(() => {
    async function fetchMatchedJobs() {
      if (!user) return

      const supabase = createClient()
      const { data, error } = await supabase
        .from('user_job_links')
        .select(`
          job_id,
          date_sent,
          job_listings (
            id,
            title,
            company_name,
            location,
            location_type,
            url,
            date_posted,
            category_dept,
            description,
            years_of_experience,
            companies (
              logo_url
            )
          )
        `)
        .eq('user_id', user.id)
        .order('date_sent', { ascending: false })

      if (error) {
        console.error('Error fetching matched jobs:', error)
      } else if (data) {
        // Transform the data to match our JobMatch interface
        const transformedData: JobMatch[] = (data as unknown as SupabaseResponse[]).map(item => ({
          job_id: item.job_id,
          date_sent: item.date_sent,
          job_listings: {
            id: item.job_listings.id,
            title: item.job_listings.title,
            company_name: item.job_listings.company_name,
            location: item.job_listings.location,
            location_type: item.job_listings.location_type,
            url: item.job_listings.url,
            date_posted: item.job_listings.date_posted,
            category_dept: item.job_listings.category_dept,
            description: item.job_listings.description,
            years_of_experience: item.job_listings.years_of_experience,
            companies: item.job_listings.companies
          }
        }))

        // --- BEGIN: Filter jobs by last 14 days or fallback to first 10 ---
        // Get today's date and the date 14 days ago
        const now = new Date();
        const fourteenDaysAgo = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);

        // Filter jobs sent in the last 14 days
        const recentJobs = transformedData.filter(job => {
          // Parse the date_sent string into a Date object
          const sentDate = new Date(job.date_sent);
          return sentDate >= fourteenDaysAgo;
        });

        let jobsToShow: JobMatch[] = [];
        if (recentJobs.length > 0) {
          // If there are recent jobs, show them all
          jobsToShow = recentJobs;
        } else if (transformedData.length > 0) {
          // If no recent jobs, show the first 10 jobs of all time (if any)
          jobsToShow = transformedData.slice(0, 10);
        } // else, jobsToShow stays empty (empty state will show)

        // Set the jobs to display
        setMatchedJobs(jobsToShow);
        // --- END: Filter jobs by last 14 days or fallback to first 10 ---
      }
      setLoading(false)
    }

    fetchMatchedJobs()
  }, [user])

  const handleAIInsightsClick = (jobId: number) => {
    if (!isProUser) {
      setUpgradeFeature('insights')
      setUpgradeModalOpen(true)
      return
    }
    if (selectedJobId === jobId) {
      setSelectedJobId(null);
      return;
    }
    if (isLimited) {
      toast.error('You have reached your daily limit for AI Insights. Please try again tomorrow.');
      return;
    }
    if (incrementUsage()) {
      setSelectedJobId(jobId);
    } else {
      toast.error('You have reached your daily limit for AI Insights. Please try again tomorrow.');
    }
  };

  const handleHiringManagersClick = (jobId: number) => {
    if (!isProUser) {
      setUpgradeFeature('contacts')
      setUpgradeModalOpen(true)
      return
    }
    if (selectedHiringManagersJobId === jobId) {
      setSelectedHiringManagersJobId(null);
      return;
    }
    if (isLimited) {
      toast.error('You have reached your daily limit for AI features. Please try again tomorrow.');
      return;
    }
    if (incrementUsage()) {
      setSelectedHiringManagersJobId(jobId);
    } else {
      toast.error('You have reached your daily limit for AI features. Please try again tomorrow.');
    }
  };

  return (
    <ProtectedRoute>
      <DashboardLayout>
        <div className="p-3 sm:p-8">
          <div className="max-w-5xl mx-auto">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 mb-4 sm:mb-6">
              <h1 className="text-xl sm:text-3xl font-bold text-gray-900">Your Matched Jobs</h1>
              {/* {!isLimited && (
                <div className="text-sm text-gray-600">
                  {remainingCalls} AI Insights remaining today
                </div>
              )} */}
            </div>

            {loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 sm:h-16 sm:w-16 border-t-2 border-b-2 border-gray-900"></div>
              </div>
            ) : matchedJobs.length > 0 ? (
              <div className="grid gap-3 sm:gap-4">
                {matchedJobs.map((match) => {
                  const isNew = Boolean(match.job_listings.date_posted && 
                    new Date().getTime() - new Date(match.job_listings.date_posted).getTime() < 24 * 60 * 60 * 1000);
                  
                  // Transform the job data to match UnifiedJobCard interface
                  const jobData = {
                    id: match.job_listings.id,
                    title: match.job_listings.title,
                    company_name: match.job_listings.company_name,
                    location: match.job_listings.location,
                    location_type: match.job_listings.location_type,
                    url: match.job_listings.url,
                    date_posted: match.job_listings.date_posted,
                    category_dept: match.job_listings.category_dept,
                    description: match.job_listings.description,
                    years_of_experience: match.job_listings.years_of_experience,
                    companies: match.job_listings.companies
                  };
                  
                  return (
                    <UnifiedJobCard
                      key={`${match.job_id}-${match.job_listings.id}`}
                      job={jobData}
                      context="dashboard"
                      isAuthenticated={Boolean(user)}
                      isProUser={isProUser}
                      timePosted={formatTimeAgo(match.date_sent)}
                      showAIFeatures={true}
                      isLimited={isLimited}
                      onAIInsightsClick={handleAIInsightsClick}
                      onKeyContactsClick={handleHiringManagersClick}
                      showingInsights={selectedJobId === match.job_listings.id}
                      showingContacts={selectedHiringManagersJobId === match.job_listings.id}
                      isNew={isNew}
                    />
                  );
                })}
              </div>
            ) : (
              // New empty state for free users (upsell)
              !isProUser ? (
                <div className="w-full max-w-5xl mx-auto px-2 sm:px-8 py-4 sm:py-6 flex flex-col gap-4 sm:gap-6 items-center sm:items-start">
                  {/* Bordered, compact empty state message */}
                  <div className="w-full bg-white rounded-xl border border-gray-200 p-4 sm:p-5 flex flex-col items-center sm:items-start gap-1 mb-2">
                    <div className="text-sm sm:text-base text-gray-700">No matched jobs yet.</div>
                    <div className="text-xs sm:text-sm text-gray-500">{"You'll see jobs show up here once you've been alerted with a match."}</div>
                  </div>

                  {/* Pro Example Section */}
                  <div className="w-full bg-white rounded-xl border border-gray-200 p-4 sm:p-8 flex flex-col gap-4 sm:gap-6">
                    <div className="text-left text-base sm:text-lg font-semibold text-gray-900 mb-1 sm:mb-2">{"Here's what you unlock with Pro"}</div>
                    {/* Example Job Card */}
                    <UnifiedJobCard
                      job={{
                        id: 999999,
                        title: "Software Engineer",
                        company_name: "Anthropic",
                        location: "San Francisco, CA",
                        location_type: "Onsite",
                        url: null,
                        date_posted: null,
                        category_dept: "Engineering",
                        description: null,
                        years_of_experience: 3,
                        companies: null
                      }}
                      context="preview"
                      isAuthenticated={false}
                      isProUser={false}
                      timePosted="1 hour ago"
                      showAIFeatures={false}
                      isLimited={false}
                      isNew={false}
                      className="mb-2 sm:mb-4"
                    />

                    {/* Example Feature Panels */}
                    <div className="w-full flex flex-col gap-4 sm:flex-row sm:gap-6">
                      {/* Suggested Contacts Panel */}
                      <div className="flex-1 bg-gray-50 rounded-lg p-3 sm:p-6 border border-gray-100 flex flex-col gap-3">
                        <div className="font-semibold text-gray-900 mb-2 text-left">Suggested key contacts to network with</div>
                        {/* Contact Card 1 */}
                        <div className="flex items-start gap-3 bg-white border border-gray-200 rounded-lg px-3 py-3 sm:py-5">
                          <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center font-bold text-gray-500 text-lg">JD</div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <div className="font-medium text-gray-900 text-base">John Doe</div>
                              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Highly relevant</span>
                            </div>
                            <div className="text-xs text-gray-500 mb-1">Engineering Manager</div>
                            <div className="text-xs text-gray-500">Why: John manages the team this role is on and has a history of hiring for similar positions.</div>
                          </div>
                        </div>
                        {/* Contact Card 2 */}
                        <div className="flex items-start gap-3 bg-white border border-gray-200 rounded-lg px-3 py-3 sm:py-5">
                          <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center font-bold text-gray-500 text-lg">JS</div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <div className="font-medium text-gray-900 text-base">Jane Smith</div>
                              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">2nd degree</span>
                            </div>
                            <div className="text-xs text-gray-500 mb-1">Lead Software Engineer</div>
                            <div className="text-xs text-gray-500">Why: Jane is a senior engineer on the team and a mutual connection can introduce you.</div>
                          </div>
                        </div>
                      </div>
                      {/* AI Insights Panel */}
                      <div className="flex-1 bg-gray-50 rounded-lg p-3 sm:p-6 border border-gray-100 flex flex-col justify-between mt-4 sm:mt-0">
                        <div>
                          <div className="font-semibold text-gray-900 mb-2 text-left">AI Insights Preview</div>
                          <div className="text-sm text-gray-800 mb-2 text-left font-medium">Key insights about this job:</div>
                          <ul className="bg-indigo-50 border border-indigo-100 rounded-lg px-4 py-3 text-sm text-indigo-900 text-left space-y-2 list-disc list-inside">
                            <li>Experience with large language models is highly valued.</li>
                            <li>Strong backend engineering skills are required.</li>
                            <li>Cross-functional collaboration is important for this team.</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    {/* Upgrade CTA at the bottom as a banner */}
                    <div className="w-full flex justify-center mt-4">
                      <div className="w-full sm:w-auto bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-xl px-6 py-5 flex flex-col items-center gap-3 shadow-sm">
                        <div className="text-center text-sm sm:text-base font-medium text-indigo-900">
                          Get <span className="font-semibold text-indigo-700">suggested contacts</span> and <span className="font-semibold text-purple-700">AI insights</span> when you upgrade to Pro.
                        </div>
                        <Button
                          className="h-10 text-sm sm:text-base bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 text-white rounded-lg font-semibold px-6 mt-1"
                          onClick={() => setUpgradeModalOpen(true)}
                        >
                          Upgrade to Pro
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                // Pro user: simple empty state
                <div className="text-center py-8 sm:py-12 bg-white rounded-xl border border-gray-200">
                  <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-2">No matched jobs yet</h3>
                  <p className="text-sm sm:text-base text-gray-600">
                    {"When we find jobs matching your preferences, they'll appear here."}
                  </p>
                </div>
              )
            )}
          </div>
        </div>
        <UpgradeToProModal
          open={upgradeModalOpen}
          onOpenChange={setUpgradeModalOpen}
          userId={user?.id || ''}
        />
      </DashboardLayout>
    </ProtectedRoute>
  )
} 