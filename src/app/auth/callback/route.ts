import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'

export async function GET(request: Request) {
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get('code')

  if (code) {
    const cookieStore = cookies()

    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll: () => cookieStore.getAll().map(c => ({ name: c.name, value: c.value })),
          setAll: (cookies) => cookies.forEach(c => cookieStore.set(c.name, c.value, c.options)),
        },
      }
    )

    const { data: { session } } = await supabase.auth.exchangeCodeForSession(code);
    
    // If this was a Google sign-in, update the temp filters with the actual email
    if (session?.provider_token) {
      await supabase
        .from('temp_user_filters')
        .update({ email: session.user.email })
        .eq('email', 'pending_google_auth');
    }
  }

  // For Google auth, redirect directly to complete-profile
  // For email auth, this still works as before
  return NextResponse.redirect(`${requestUrl.origin}/complete-profile`);
}
