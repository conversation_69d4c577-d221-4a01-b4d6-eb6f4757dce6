"use client";

import * as React from "react"
import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Search, X, Sparkles, Mail, MapPin, User, Rocket, Clock, Bell, Shield, Users, CheckCircle2, ChevronRight, ArrowRight, Star, Zap, Lock, Building } from 'lucide-react'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import Image from "next/image"
import Link from 'next/link'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { FilterProvider, useFilter } from '@/contexts/FilterContext'
import { JobSearchSection } from '@/components/ui/JobSearchSection'
import { JobRole } from '@/lib/roles'
import { LogoTest } from '@/components/ui/LogoTest'
import { useTypewriter, Cursor } from 'react-simple-typewriter'
import { Card, CardContent } from "@/components/ui/card"
import { inject } from '@vercel/analytics'
import { EmailJobAlertCard, HiringManagerCard } from '@/components/ui/JobPreviewCards'
import { FeaturedJobCard } from '@/components/ui/FeaturedJobCard'
import { JobAlertEmailPreview, ProFeaturePreview, EmailExamplePreview } from '@/components/ui/FeaturePreviewCards'
import { CompanyLogo } from '@/components/ui/CompanyLogo'
import { CompanySelectionSection } from '@/components/ui/CompanySelectionSection'

interface JobListing {
  id: number;
  title: string;
  description?: string;
  url?: string;
  location?: string;
  salary_range?: { [key: string]: any }; // Using any for jsonb type
  created_at?: Date;
  updated_at?: Date;
  company_name: string;
  category_dept: JobRole;  // Using our enum type
  seniority_level?: string;
  management_level?: string;
  is_manager?: boolean;
  years_of_experience?: number;
  related_skills?: string[]; // Assuming it's an array of strings
  profitable?: boolean;
  funding_round?: string;
  location_type?: string;  // Made optional and changed to string
  date_posted?: Date;
}


//TODO: Ask Ismail and Ahmed the best way to setup these types of mappings
const hotCompanies = ['OpenAI', 'Anthropic', 'Notion', 'Figma', 'Stripe']

const formatLogoUrl = (companyName: string) => {
  const domain = companyName.toLowerCase().replace(/[^a-z0-9]/g, '') + '.com';
  const url = `https://img.logo.dev/${domain}?token=${process.env.NEXT_PUBLIC_LOGO_DEV_PUBLIC_KEY}`;
  return url;
};

function HomeContent() {
  const { 
    selectedCompanies, 
    setSelectedCompanies
  } = useFilter();

  const [roleFilter, setRoleFilter] = useState<string[]>(['*']);

  const [typewriterText] = useTypewriter({
    words: ['OpenAI', 'Anthropic', 'Ramp', 'Figma', 'Notion'],
    loop: true,
    delaySpeed: 2000,
  })

  const handleRoleChange = (newRole: string[]) => {
    console.log('Home handleRoleChange:', newRole);
    setRoleFilter(newRole);
  };

  const handleSignupToSaveSearch = () => {
    const formatArrayItems = (arr: string[]): string[] =>
      arr.map(item => item.replace(/,/g, ', '));

    console.log('Saving search with role:', roleFilter);
    const currentFilters = {
      companies: formatArrayItems(selectedCompanies),
      role: roleFilter,
      alert_frequency: []
    };

    const filtersJson = JSON.stringify(currentFilters);
    const encodedFilters = encodeURIComponent(filtersJson);
    window.location.href = `/signup?filters=${encodedFilters}`;
  };

  // New function to handle company selection and redirect to signup
  const handleCreateAlert = (companies: string[], role: string[]) => {
    const formatArrayItems = (arr: string[]): string[] =>
      arr.map(item => item.replace(/,/g, ', '));

    console.log('Creating alert with:', { companies, role });
    const currentFilters = {
      companies: formatArrayItems(companies),
      role: role,
      alert_frequency: []
    };

    const filtersJson = JSON.stringify(currentFilters);
    const encodedFilters = encodeURIComponent(filtersJson);
    window.location.href = `/signup?filters=${encodedFilters}`;
  };

  // Dummy function for bundle selection - redirects to signup
  const handleBundleSelect = (companies: string[], role: string[], bundleTitle?: string) => {
    handleCreateAlert(companies, role);
  };

  // Dummy function for alert created - redirects to signup  
  const handleAlertCreated = (data: { companies: string[]; role: string[]; error?: 'already_tracking' | 'update_failed' }) => {
    if (!data.error) {
      handleCreateAlert(data.companies, data.role);
    }
  };

  useEffect(() => {
    inject(); // Initialize analytics
  }, []);

  return (
    <main className="flex min-h-screen flex-col items-center bg-white">
      {/* Header Section */}
      <header className="w-full border-b border-gray-100 bg-white/90 backdrop-blur-sm fixed top-0 z-50">
        <div className="max-w-6xl mx-auto px-6 py-4 flex items-center justify-between">
          <div className="flex items-center gap-6">
            <Link href="/" className="text-gray-900 font-bold text-xl">
              Awaloon
            </Link>
          </div>
          <div className="flex items-center gap-4">
            <Link href="/signin" className="text-gray-600 hover:text-gray-900 transition-all">
              Sign in
            </Link>
            <Link 
              href="/signup" 
              className="px-5 py-2.5 bg-[#4F46E5] hover:bg-[#4338CA] text-white text-base font-medium rounded-lg transition-all hover:-translate-y-0.5 shadow-sm"
            >
              Sign up
            </Link>
          </div>
        </div>
      </header>

      {/* Updated Hero Section */}
      <section className="w-full bg-gradient-to-b from-gray-50 to-transparent pt-20 md:pt-24 pb-8 md:pb-12 relative overflow-hidden">
        {/* Background Image/Pattern */}
        <div className="absolute inset-0 w-full h-full">
          <Image
            src="/images/hero-pattern.png"
            alt="Hero background pattern"
            fill
            className="object-cover opacity-5"
            priority
          />
        </div>

        <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8 relative">
          <div className="grid lg:grid-cols-2 gap-6 lg:gap-12 items-center">
            {/* Left Column - Text Content */}
            <div className="max-w-2xl relative z-10">
              <h1 className="text-[28px] sm:text-[32px] md:text-4xl lg:text-[44px] font-extrabold text-gray-900 leading-[1.15] md:leading-[1.1] tracking-tight mb-3 md:mb-4">
                Stop applying to jobs with 500+ applicants
              </h1>
                
              <p className="text-base md:text-lg lg:text-xl text-gray-600 mb-6 md:mb-8 leading-relaxed">
                Get fresh opportunities from top AI companies delivered before they hit LinkedIn
              </p>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 mb-6 md:mb-8">
                <button 
                  onClick={() => {
                    const searchSection = document.querySelector('#search-section');
                    searchSection?.scrollIntoView({ behavior: 'smooth' });
                  }}
                  className="h-11 md:h-12 lg:h-14 px-5 md:px-6 bg-[#4F46E5] hover:bg-[#4338CA] text-white text-sm md:text-base lg:text-lg font-medium rounded-lg transition-all hover:-translate-y-0.5 shadow-sm flex items-center justify-center gap-2 group"
                >
                  See How It Works
                  <ArrowRight className="w-4 h-4 md:w-5 md:h-5 group-hover:translate-x-0.5 transition-transform" />
                </button>
                <Link 
                  href="/signup" 
                  className="h-11 md:h-12 lg:h-14 px-5 md:px-6 bg-white border-2 border-gray-200 hover:border-gray-300 text-gray-700 text-sm md:text-base lg:text-lg font-medium rounded-lg transition-all hover:-translate-y-0.5 shadow-sm flex items-center justify-center"
                >
                  Get Started Free
                </Link>
              </div>

              {/* Trust Badge */}
              <div className="flex items-center gap-2 md:gap-3">
                <div className="flex -space-x-1.5 md:-space-x-2">
                  {['OpenAI', 'Anthropic', 'Notion'].map((company, i) => (
                    <div key={company} className="w-7 h-7 md:w-8 md:h-8 rounded-full bg-white border-2 border-white shadow-sm overflow-hidden">
                      <CompanyLogo
                        name={company}
                        logoUrl={null}
                        size={32}
                        className="w-full h-full"
                      />
                    </div>
                  ))}
                </div>
                <p className="text-xs md:text-sm text-gray-600">
                  Used by job seekers targeting OpenAI, Anthropic, Notion, and more
                </p>
              </div>
            </div>

            {/* Right Column - Hero Image */}
            <div className="lg:pl-8 relative z-10 mt-6 lg:mt-0">
              <div className="relative">
                <div className="transform hover:-translate-y-2 transition-transform duration-300">
                  <Image
                    src="/images/Awaloon_hero.png"
                    alt="Job alert preview with company logos"
                    width={600}
                    height={500}
                    className="rounded-xl shadow-lg w-full h-auto"
                    priority
                  />
                  {/* Subtle glow effect */}
                  <div className="absolute -inset-1 bg-gradient-to-r from-indigo-500 to-purple-500 opacity-10 blur-xl rounded-xl -z-10" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Job Searching is Broken Section */}
      <section className="w-full bg-gray-50 py-12 md:py-16">
        <div className="max-w-7xl mx-auto px-6">
          {/* Section Header */}
          <div className="text-center mb-8 md:mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 tracking-tight">
              Job searching is <span className="bg-gradient-to-r from-[#7c3aed] to-[#6366f1] bg-clip-text text-transparent">broken.</span>
            </h2>
            <p className="text-lg md:text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
              {"Here's how we're fixing it."}
            </p>
          </div>

          {/* Carousel Container */}
          <div className="relative">
            {/* Cards Container - Horizontal Scroll */}
            <div className="flex gap-4 md:gap-6 overflow-x-auto pb-4 snap-x snap-mandatory scrollbar-hide">
              
              {/* Card 1: Speed Problem → Fresh Jobs Solution */}
              <div className="group flex-shrink-0 w-[280px] md:w-[320px] lg:w-[360px] snap-center">
                <div className="bg-white rounded-xl border border-gray-200 p-5 md:p-6 hover:shadow-xl transition-all duration-500 hover:-translate-y-1 relative overflow-hidden h-full">
                  {/* Gradient overlay that appears on hover */}
                  <div className="absolute inset-0 bg-gradient-to-br from-indigo-50/0 to-indigo-100/0 group-hover:from-indigo-50/30 group-hover:to-indigo-100/20 transition-all duration-500 rounded-xl" />
                  
                  <div className="relative z-10">
                    {/* Problem Statement */}
                    <div className="mb-5">
                      <p className="text-base md:text-lg font-semibold text-gray-700 leading-tight mb-3">
                        Listings get hundreds of applications on the first day
                      </p>
                      
                      {/* Visual Transition Arrow */}
                      <div className="flex items-center justify-center my-3">
                        <div className="flex items-center gap-2">
                          <div className="w-4 h-px bg-gradient-to-r from-gray-300 to-indigo-300" />
                          <div className="w-5 h-5 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-full flex items-center justify-center transform group-hover:scale-110 transition-transform duration-300">
                            <ArrowRight className="w-2.5 h-2.5 text-white" />
                          </div>
                          <div className="w-4 h-px bg-gradient-to-r from-indigo-300 to-indigo-500" />
                        </div>
                      </div>
                    </div>

                    {/* Solution Statement */}
                    <div className="bg-gradient-to-br from-indigo-50 to-indigo-100/50 rounded-lg p-3 border border-indigo-100">
                      <p className="text-base md:text-lg font-bold text-indigo-900 leading-tight">
                        We email you the hottest jobs daily directly from company career pages
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Card 2: Relevance Problem → Targeted Solution */}
              <div className="group flex-shrink-0 w-[280px] md:w-[320px] lg:w-[360px] snap-center">
                <div className="bg-white rounded-xl border border-gray-200 p-5 md:p-6 hover:shadow-xl transition-all duration-500 hover:-translate-y-1 relative overflow-hidden h-full">
                  {/* Gradient overlay that appears on hover */}
                  <div className="absolute inset-0 bg-gradient-to-br from-indigo-50/0 to-indigo-100/0 group-hover:from-indigo-50/30 group-hover:to-indigo-100/20 transition-all duration-500 rounded-xl" />
                  
                  <div className="relative z-10">
                    {/* Problem Statement */}
                    <div className="mb-5">
                      <p className="text-base md:text-lg font-semibold text-gray-700 leading-tight mb-3">
                        Job boards are flooded with sponsored and irrelevant roles
                      </p>
                      
                      {/* Visual Transition Arrow */}
                      <div className="flex items-center justify-center my-3">
                        <div className="flex items-center gap-2">
                          <div className="w-4 h-px bg-gradient-to-r from-gray-300 to-indigo-300" />
                          <div className="w-5 h-5 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-full flex items-center justify-center transform group-hover:scale-110 transition-transform duration-300">
                            <ArrowRight className="w-2.5 h-2.5 text-white" />
                          </div>
                          <div className="w-4 h-px bg-gradient-to-r from-indigo-300 to-indigo-500" />
                        </div>
                      </div>
                    </div>

                    {/* Solution Statement */}
                    <div className="bg-gradient-to-br from-indigo-50 to-indigo-100/50 rounded-lg p-3 border border-indigo-100">
                      <p className="text-base md:text-lg font-bold text-indigo-900 leading-tight">
                        We let you pick your roles and companies so you only see what you care about
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Card 3: Response Problem → Networking Solution */}
              <div className="group flex-shrink-0 w-[280px] md:w-[320px] lg:w-[360px] snap-center">
                <div className="bg-white rounded-xl border border-gray-200 p-5 md:p-6 hover:shadow-xl transition-all duration-500 hover:-translate-y-1 relative overflow-hidden h-full">
                  {/* Gradient overlay that appears on hover */}
                  <div className="absolute inset-0 bg-gradient-to-br from-indigo-50/0 to-indigo-100/0 group-hover:from-indigo-50/30 group-hover:to-indigo-100/20 transition-all duration-500 rounded-xl" />
                  
                  <div className="relative z-10">
                    {/* Problem Statement */}
                    <div className="mb-5">
                      <p className="text-base md:text-lg font-semibold text-gray-700 leading-tight mb-3">
                        {"Cold applications are dead. It's like submitting into the void"}
                      </p>
                      
                      {/* Visual Transition Arrow */}
                      <div className="flex items-center justify-center my-3">
                        <div className="flex items-center gap-2">
                          <div className="w-4 h-px bg-gradient-to-r from-gray-300 to-indigo-300" />
                          <div className="w-5 h-5 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-full flex items-center justify-center transform group-hover:scale-110 transition-transform duration-300">
                            <ArrowRight className="w-2.5 h-2.5 text-white" />
                          </div>
                          <div className="w-4 h-px bg-gradient-to-r from-indigo-300 to-indigo-500" />
                        </div>
                      </div>
                    </div>

                    {/* Solution Statement */}
                    <div className="bg-gradient-to-br from-indigo-50 to-indigo-100/50 rounded-lg p-3 border border-indigo-100">
                      <p className="text-base md:text-lg font-bold text-indigo-900 leading-tight">
                        We suggest relevant contacts for each role so you can network with the right people
                      </p>
                    </div>
                  </div>
                </div>
              </div>

            </div>

            {/* Scroll Indicators */}
            <div className="flex justify-center mt-4 gap-2 md:hidden">
              <div className="w-2 h-2 rounded-full bg-indigo-300"></div>
              <div className="w-2 h-2 rounded-full bg-gray-300"></div>
              <div className="w-2 h-2 rounded-full bg-gray-300"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Search Section - UPDATED */}
      <section id="search-section" className="w-full px-6 py-16 scroll-mt-24">
        <div className="max-w-7xl mx-auto">
          {/* Clean One-Line Header */}
          <div className="text-center mb-8">
            <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900">
              Select a role and companies to track. Get alerts starting tomorrow.
            </h2>
          </div>
          
          <div className="bg-white rounded-xl border border-gray-200 p-8">
            {/* Replace JobSearchSection with CompanySelectionSection */}
            <CompanySelectionSection
              currentRole={roleFilter}
              onRoleChange={async (newRole: string[]) => {
                setRoleFilter(newRole);
              }}
              onSelectBundle={handleBundleSelect}
              onAlertCreated={handleAlertCreated}
              addedBundles={[]} // No bundles added for non-authenticated users
              initialCompanies={[]} // Start fresh
              isAuthenticated={false} // Homepage users are not authenticated
              onSignupClick={handleCreateAlert} // Pass the signup handler
            />
          </div>
        </div>
      </section>

      {/* What You Get with Awaloon Section */}
      <section className="w-full px-6 py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              What You Get with Awaloon
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Everything you need to land your next role — faster, smarter, and with an edge.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {/* Job Alerts Card */}
            <div className="group">
              <div className="bg-white rounded-xl border border-gray-200 p-6 h-full hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                {/* Label */}
                <div className="mb-4">
                  <span className="text-sm font-medium text-green-600 bg-green-50 px-3 py-1 rounded-full">
                    Free Plan Feature
                  </span>
                </div>

                {/* Icon & Title */}
                <div className="flex items-start gap-4 mb-6">
                  <div className="w-12 h-12 rounded-xl bg-indigo-50 flex items-center justify-center text-2xl group-hover:scale-110 transition-transform duration-300">
                    ✉️
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 text-lg">Job Alerts</h3>
                    <p className="text-gray-600 mt-1">Get notified the moment a new role is posted.</p>
                  </div>
                </div>

                {/* Preview */}
                <div className="bg-gray-50 rounded-lg p-4 border border-gray-100">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-8 h-8 rounded-lg bg-white border border-gray-200 flex items-center justify-center">
                      <CompanyLogo
                        name="OpenAI"
                        logoUrl={null}
                        size={20}
                        className="w-full h-full"
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-gray-900 text-sm truncate">New role at OpenAI</p>
                      <p className="text-xs text-gray-500">Posted 5 minutes ago</p>
                    </div>
                    <span className="px-2 py-1 bg-red-50 text-red-600 text-xs font-medium rounded-full animate-pulse">Just Posted</span>
                  </div>
                  <button className="w-full bg-indigo-50 text-indigo-600 py-2 px-3 rounded text-sm font-medium hover:bg-indigo-100 transition-colors mt-3">
                    View Example
                  </button>
                </div>
              </div>
            </div>

            {/* Hiring Team Access Card */}
            <div className="group">
              <div className="bg-gradient-to-br from-indigo-50 via-white to-white rounded-xl border border-indigo-100 p-6 h-full hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                {/* Label */}
                <div className="mb-4">
                  <span className="text-sm font-medium text-indigo-600 bg-indigo-50 px-3 py-1 rounded-full">
                    Pro Plan Feature
                  </span>
                </div>

                {/* Icon & Title */}
                <div className="flex items-start gap-4 mb-6">
                  <div className="w-12 h-12 rounded-xl bg-indigo-100 flex items-center justify-center text-2xl group-hover:scale-110 transition-transform duration-300">
                    👤
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 text-lg">Hiring Team Access</h3>
                    <p className="text-gray-600 mt-1">See exactly who to reach out to — verified contacts at top companies.</p>
                  </div>
                </div>

                {/* Preview */}
                <div className="relative">
                  <div className="bg-white rounded-lg p-4 border border-indigo-100">
                    <div className="space-y-4">
                      {/* Contact Preview */}
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                          <span className="text-sm font-medium text-gray-600">AC</span>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">Alex Chen</p>
                          <p className="text-xs text-gray-600">Engineering Lead</p>
                        </div>
                      </div>
                      {/* Message Preview */}
                      <div className="bg-gray-50 rounded p-3">
                        <p className="text-xs text-gray-600 line-clamp-2">
                          {"\"Hi Alex, I saw your team's work on... [Suggested message]\""}
                        </p>
                      </div>
                    </div>
                  </div>
                  {/* Unlock Overlay */}
                  <div className="absolute inset-0 bg-white/80 backdrop-blur-[1px] rounded-lg flex items-center justify-center">
                    <button className="flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition-colors">
                      <Lock className="w-4 h-4" />
                      Unlock Contact
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* AI Interview Insights Card */}
            <div className="group">
              <div className="bg-gradient-to-br from-indigo-50 via-white to-white rounded-xl border border-indigo-100 p-6 h-full hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                {/* Label */}
                <div className="mb-4">
                  <span className="text-sm font-medium text-indigo-600 bg-indigo-50 px-3 py-1 rounded-full">
                    Pro Plan Feature
                  </span>
                </div>

                {/* Icon & Title */}
                <div className="flex items-start gap-4 mb-6">
                  <div className="w-12 h-12 rounded-xl bg-indigo-100 flex items-center justify-center text-2xl group-hover:scale-110 transition-transform duration-300">
                    🤖
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 text-lg">Interview Prep</h3>
                    <p className="text-gray-600 mt-1">Get role-specific questions + smart tips before you apply.</p>
                  </div>
                </div>

                {/* Preview */}
                <div className="relative">
                  <div className="bg-white rounded-lg p-4 border border-indigo-100">
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 rounded-full bg-indigo-50 flex items-center justify-center">
                          <Sparkles className="w-4 h-4 text-indigo-600" />
                        </div>
                        <p className="text-sm text-gray-900">5 Likely Questions Generated</p>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 rounded-full bg-indigo-50 flex items-center justify-center">
                          <Building className="w-4 h-4 text-indigo-600" />
                        </div>
                        <p className="text-sm text-gray-900">Company Research Tips</p>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 rounded-full bg-indigo-50 flex items-center justify-center">
                          <Star className="w-4 h-4 text-indigo-600" />
                        </div>
                        <p className="text-sm text-gray-900">Role-specific Advice</p>
                      </div>
                    </div>
                  </div>
                  {/* Unlock Overlay */}
                  <div className="absolute inset-0 bg-white/80 backdrop-blur-[1px] rounded-lg flex items-center justify-center">
                    <button className="flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition-colors">
                      <Lock className="w-4 h-4" />
                      Preview AI Tips
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="w-full px-6 py-16 bg-gradient-to-br from-indigo-500 to-purple-600 text-white">
        <div className="mx-auto" style={{ maxWidth: '1400px' }}>
          <div className="flex flex-col lg:flex-row items-center justify-between gap-8">
            <div className="max-w-2xl">
              <h2 className="text-3xl sm:text-4xl font-bold mb-4">
                Ready to land your dream job?
              </h2>
              <p className="text-xl text-indigo-100 mb-6">
                Join thousands of job seekers who get notified first about the best opportunities.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link 
                  href="/signup" 
                  className="inline-flex items-center justify-center px-6 py-3 bg-white text-indigo-600 rounded-lg font-medium hover:bg-indigo-50 transition-colors"
                >
                  {"Get Started - It's Free"}
                </Link>
                <Link 
                  href="/signup?plan=pro" 
                  className="inline-flex items-center justify-center px-6 py-3 bg-indigo-400 bg-opacity-20 text-white rounded-lg font-medium hover:bg-opacity-30 transition-colors"
                >
                  Upgrade to Pro
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Link>
              </div>
            </div>
            <div className="lg:w-1/3">
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <div className="flex items-center gap-3 mb-4">
                  <div className="bg-white/10 rounded-lg p-2.5">
                    <Zap className="w-6 h-6 text-yellow-300" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Why Job Seekers Choose Us</h3>
                    <p className="text-sm text-indigo-100">Trusted by professionals</p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="flex items-start gap-2">
                    <CheckCircle2 className="w-5 h-5 text-green-300 mt-0.5" />
                    <div>
                      <span className="block font-medium">Real-time Monitoring</span>
                      <span className="text-sm text-indigo-100">We check company career pages every few minutes</span>
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <CheckCircle2 className="w-5 h-5 text-green-300 mt-0.5" />
                    <div>
                      <span className="block font-medium">Direct from Source</span>
                      <span className="text-sm text-indigo-100">No third-party job boards or aggregators</span>
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <CheckCircle2 className="w-5 h-5 text-green-300 mt-0.5" />
                    <div>
                      <span className="block font-medium">Zero Spam</span>
                      <span className="text-sm text-indigo-100">Only receive alerts for roles you want</span>
                    </div>
                  </div>
                  <div className="mt-6 pt-4 border-t border-white/10">
                    <div className="flex items-center gap-2 text-sm">
                      <Zap className="w-4 h-4 text-yellow-300" />
                      <span>Tracking 100+ top tech companies</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Add Attribution Footer */}
      <footer className="w-full py-4 mt-auto">
        <div className="max-w-6xl mx-auto px-6 text-center">
          <p className="text-sm text-gray-500">
            Logos provided by{' '}
            <a 
              href="https://logo.dev" 
              target="_blank" 
              rel="noopener"
              className="text-[#4F46E5] hover:underline"
            >
              Logo.dev
            </a>
          </p>
        </div>
      </footer>
    </main>
  );
}

export default function Home() {
  return (
    <FilterProvider>
      <HomeContent />
    </FilterProvider>
  )
}
