'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/lib/supabase';

const supabase = createClient();

const standardizeRoleFilter = (role: string | string[] | null): string[] => {
  if (!role) return [];
  if (role === 'all') return ['*'];
  if (Array.isArray(role)) return role;
  return [role];
};

export default function CompleteProfile() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const updateProfile = async () => {
      try {
        const { data: { user }, error: userError } = await supabase.auth.getUser();
        if (userError) throw userError;

        if (!user) {
          console.log('No authenticated user found, redirecting to login');
          return router.push('/login');
        }

        // Retrieve temporary filters
        const { data: tempFilters, error: filterError } = await supabase
          .from('temp_user_filters')
          .select('filters')
          .eq('email', user.email)
          .single();
        
        if (filterError) {
          console.error('Error fetching temp filters:', filterError);
          // Don't throw here - user might not have filters
        }

        if (tempFilters?.filters) {
          // Update user profile with all filter fields
          const { error: updateError } = await supabase
            .from('user_profiles')
            .update({
              is_verified: true,
              filter_companies: tempFilters.filters.companies || [],
              filter_role: standardizeRoleFilter(tempFilters.filters.role),
              filter_experience: tempFilters.filters.experience || null,
              filter_location: tempFilters.filters.location || null,
              filter_alert_frequency: tempFilters.filters.alert_frequency || 'daily',
            })
            .eq('id', user.id);

          if (updateError) throw updateError;

          // Clean up temp filters
          const { error: deleteError } = await supabase
            .from('temp_user_filters')
            .delete()
            .eq('email', user.email);

          if (deleteError) {
            console.error('Error cleaning up temp filters:', deleteError);
            // Don't throw - not critical
          }
        }

        router.push('/dashboard');
      } catch (error) {
        console.error('Error in profile completion:', error);
        setError(error instanceof Error ? error.message : 'An unexpected error occurred');
        // Stay on page to show error instead of redirecting -
      } finally {
        setLoading(false);
      }
    };

    updateProfile();
  }, [router]);

  // Add loading and error UI - 
  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Setting up your profile...</h2>
          <p className="text-gray-600">This will only take a moment</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2 text-red-600">Something went wrong</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return null; // Normal case - will redirect before rendering
}
