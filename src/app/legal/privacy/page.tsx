export default function PrivacyPolicy() {
    return (
      <div className="max-w-4xl mx-auto px-4 py-16">
        <h1 className="text-3xl font-bold mb-8">Privacy Policy</h1>
        
        <div className="prose">
          <p className="text-gray-600 mb-4">Last updated: {new Date().toLocaleDateString()}</p>
          
          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">1. Information We Collect</h2>
            <p>When you use First to Apply, we collect information that you provide directly to us:</p>
            <ul className="list-disc pl-6 mb-4">
              <li>Name and email address when you create an account</li>
              <li>Job preferences and search criteria</li>
              <li>Information from your Google account if you choose to sign in with Google</li>
            </ul>
          </section>
  
          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">2. How We Use Your Information</h2>
            <p>We use the information we collect to:</p>
            <ul className="list-disc pl-6 mb-4">
              <li>Provide, maintain, and improve our services</li>
              <li>Send you job alerts and notifications</li>
              <li>Communicate with you about our services</li>
            </ul>
          </section>
  
          <p className="text-sm text-gray-500 mt-8">
            This is a placeholder privacy policy for development purposes. 
            A comprehensive privacy policy will be implemented before public release.
          </p>
        </div>
      </div>
    );
  }