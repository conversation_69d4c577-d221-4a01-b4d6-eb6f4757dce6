export default function TermsOfService() {
    return (
      <div className="max-w-4xl mx-auto px-4 py-16">
        <h1 className="text-3xl font-bold mb-8">Terms of Service</h1>
        
        <div className="prose">
          <p className="text-gray-600 mb-4">Last updated: {new Date().toLocaleDateString()}</p>
          
          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">1. Acceptance of Terms</h2>
            <p>
              By accessing and using First to Apply, you accept and agree to be bound by the terms
              and conditions of this agreement.
            </p>
          </section>
  
          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">2. Description of Service</h2>
            <p>
              First to Apply is a job search platform that helps users find and apply to job
              opportunities quickly. We provide job alerts and application tracking services.
            </p>
          </section>
  
          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">3. User Accounts</h2>
            <p>
              You may be required to create an account to use certain features of our service.
              You are responsible for maintaining the confidentiality of your account information.
            </p>
          </section>
  
          <p className="text-sm text-gray-500 mt-8">
            This is a placeholder terms of service for development purposes. 
            Comprehensive terms will be implemented before public release.
          </p>
        </div>
      </div>
    );
  }