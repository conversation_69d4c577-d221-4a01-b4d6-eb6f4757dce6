'use client';

import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';

export default function PaymentCanceledPage() {
  const router = useRouter();

  return (
    <div className="flex min-h-screen flex-col items-center justify-center space-y-4 text-center">
      <h1 className="text-4xl font-bold text-gray-900">Payment Canceled</h1>
      <p className="text-lg text-gray-600">
        Your payment was canceled. No charges were made.
      </p>
      <div className="space-x-4">
        <Button 
          variant="outline" 
          onClick={() => router.back()}
        >
          Try Again
        </Button>
        <Button 
          onClick={() => router.push('/dashboard')}
        >
          Return to Dashboard
        </Button>
      </div>
    </div>
  );
} 