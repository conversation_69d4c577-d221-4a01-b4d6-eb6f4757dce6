'use client';

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { CheckCircle, Loader2 } from 'lucide-react';
import { createClient } from '@/lib/supabase';

// Add dynamic export
export const dynamic = 'force-dynamic';

function PaymentSuccessContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const checkPaymentStatus = async () => {
      try {
        const sessionId = searchParams.get('session_id');
        if (!sessionId) {
          setError('No session ID found');
          setIsLoading(false);
          return;
        }

        // Log the session ID
        console.log('Payment session ID:', sessionId);

        // Wait a moment for webhook to process
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Initialize Supabase client and get session
        const supabase = createClient();
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError || !session) {
          console.error('Error getting session:', sessionError);
          setError('Unable to verify user session');
          setIsLoading(false);
          return;
        }
        
        // Get current user from session
        const user = session.user;
        
        if (!user) {
          console.error('No user in session');
          setError('Unable to verify user session');
          setIsLoading(false);
          return;
        }

        // Check if the user's plan has been updated
        const { data: profile, error: profileError } = await supabase
          .from('user_profiles')
          .select('plan')
          .eq('id', user.id)
          .single();

        if (profileError) {
          console.error('Error fetching profile:', profileError);
          setError('Error verifying subscription status');
          setIsLoading(false);
          return;
        }

        if (!profile || profile.plan !== 'pro') {
          console.log('Current plan:', profile?.plan);
          setError('Subscription update pending. Please try refreshing in a few moments.');
          setIsLoading(false);
          return;
        }

        setIsLoading(false);
      } catch (err) {
        console.error('Error checking payment status:', err);
        setError('Something went wrong while confirming your payment');
        setIsLoading(false);
      }
    };

    checkPaymentStatus();
  }, [searchParams]);

  if (error) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4">
        <div className="w-full max-w-md rounded-lg bg-white p-8 shadow-lg">
          <div className="flex flex-col items-center space-y-4 text-center">
            <h1 className="text-2xl font-bold text-red-600">Error</h1>
            <p className="text-gray-600">{error}</p>
            <Button
              onClick={() => router.push('/dashboard/settings')}
              className="mt-4 w-full"
            >
              Return to Settings
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4">
        <div className="w-full max-w-md rounded-lg bg-white p-8 shadow-lg">
          <div className="flex flex-col items-center space-y-4 text-center">
            <Loader2 className="h-16 w-16 animate-spin text-blue-500" />
            <h1 className="text-2xl font-bold text-gray-900">Processing Payment</h1>
            <p className="text-gray-600">
              Please wait while we confirm your payment...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4">
      <div className="w-full max-w-md rounded-lg bg-white p-8 shadow-lg">
        <div className="flex flex-col items-center space-y-4 text-center">
          <CheckCircle className="h-16 w-16 text-green-500" />
          <h1 className="text-2xl font-bold text-gray-900">Payment Successful!</h1>
          <p className="text-gray-600">
            Thank you for your subscription. Your account has been upgraded to Pro!
          </p>
          <Button
            onClick={() => router.push('/dashboard')}
            className="mt-4 w-full"
          >
            Return to Dashboard
          </Button>
        </div>
      </div>
    </div>
  );
}

export default function PaymentSuccess() {
  return (
    <Suspense fallback={
      <div className="flex min-h-screen flex-col items-center justify-center p-4">
        <div className="w-full max-w-md rounded-lg bg-white p-8 shadow-lg">
          <div className="flex flex-col items-center space-y-4 text-center">
            <Loader2 className="h-16 w-16 animate-spin text-blue-500" />
            <h1 className="text-2xl font-bold text-gray-900">Loading...</h1>
          </div>
        </div>
      </div>
    }>
      <PaymentSuccessContent />
    </Suspense>
  );
} 