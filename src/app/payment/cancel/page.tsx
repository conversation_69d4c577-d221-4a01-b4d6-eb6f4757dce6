'use client';

import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { XCircle } from 'lucide-react'

export default function PaymentCancel() {
  const router = useRouter()

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4">
      <div className="w-full max-w-md rounded-lg bg-white p-8 shadow-lg">
        <div className="flex flex-col items-center space-y-4 text-center">
          <XCircle className="h-16 w-16 text-red-500" />
          <h1 className="text-2xl font-bold text-gray-900">Payment Cancelled</h1>
          <p className="text-gray-600">
            Your payment was cancelled. No charges were made to your account.
          </p>
          <div className="flex w-full flex-col gap-2">
            <Button
              onClick={() => router.push('/dashboard/settings')}
              className="w-full"
              variant="default"
            >
              Try Again
            </Button>
            <Button
              onClick={() => router.push('/dashboard')}
              className="w-full"
              variant="outline"
            >
              Return to Dashboard
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
} 