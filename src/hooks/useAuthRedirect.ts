import { useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useAuth as useAuthContext } from '@/contexts/AuthContext'

// Define public and protected paths
const PUBLIC_PATHS = ['/', '/signin', '/signup', '/auth/callback']
const PROTECTED_PATHS = ['/dashboard', '/settings', '/profile', '/complete-profile']

export function useAuthRedirect() {
  const { user, loading } = useAuthContext()
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    if (loading) return

    const isPublicPath = PUBLIC_PATHS.includes(pathname)
    const isProtectedPath = PROTECTED_PATHS.includes(pathname)

    if (user) {
      // Only redirect to dashboard if user is on signin or signup page
      if (pathname === '/signin' || pathname === '/signup') {
        router.push('/dashboard')
      }
    } else {
      // If user is not logged in and on a protected page, redirect to signin
      // But only if we're not already on the signin page to prevent loops
      if (isProtectedPath && pathname !== '/signin') {
        router.push('/signin')
      }
    }
  }, [user, loading, pathname, router])

  return { user, loading }
}