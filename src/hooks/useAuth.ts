import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase'

export function useAuth() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [attempts, setAttempts] = useState(0)
  const maxAttempts = 10

  const signInWithEmail = async (email: string) => {
    if (attempts >= maxAttempts) {
      setError('Too many attempts. Please try again later.')
      return { error: new Error('Rate limit exceeded') }
    }

    setLoading(true)
    setError(null)
    const supabase = createClient()
    
    try {
      // Validate email
      if (!email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
        throw new Error('Invalid email format')
      }

      setAttempts(prev => prev + 1)
      
      const { error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          emailRedirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`,
        },
      })
      
      if (error) throw error
      return { error: null }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred'
      setError(errorMessage)
      return { error: err }
    } finally {
      setLoading(false)
    }
  }

  // Reset attempts after 30 minutes
  useEffect(() => {
    const timer = setTimeout(() => setAttempts(0), 1800000)
    return () => clearTimeout(timer)
  }, [attempts])

  return { signInWithEmail, loading, error }
}
