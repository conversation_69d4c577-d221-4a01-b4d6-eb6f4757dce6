import { useState, useEffect, useCallback } from 'react'

// Interface for AI insights data
interface AIInsightData {
  jobId: number
  insights: any // The actual AI insights data
  timestamp: number
}

// Interface for key contacts data
interface KeyContactsData {
  jobId: number
  contacts: any // The actual contacts data
  timestamp: number
}

// Interface for persistent AI state
interface PersistentAIState {
  selectedJobId: number | null
  selectedHiringManagersJobId: number | null
  aiInsights: Record<number, AIInsightData>
  keyContacts: Record<number, KeyContactsData>
}

// Default state
const defaultState: PersistentAIState = {
  selectedJobId: null,
  selectedHiringManagersJobId: null,
  aiInsights: {},
  keyContacts: {}
}

// Storage key
const STORAGE_KEY = 'tracked-jobs-ai-state'

// Cache expiry time (1 hour)
const CACHE_EXPIRY = 60 * 60 * 1000

export function usePersistentAIState() {
  const [state, setState] = useState<PersistentAIState>(defaultState)

  // Load state from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY)
      if (stored) {
        const parsedState = JSON.parse(stored)
        
        // Clean up expired entries
        const now = Date.now()
        const cleanedInsights: Record<number, AIInsightData> = {}
        const cleanedContacts: Record<number, KeyContactsData> = {}
        
        // Filter out expired AI insights
        Object.entries(parsedState.aiInsights || {}).forEach(([jobId, data]: [string, any]) => {
          if (data.timestamp && (now - data.timestamp) < CACHE_EXPIRY) {
            cleanedInsights[parseInt(jobId)] = data
          }
        })
        
        // Filter out expired key contacts
        Object.entries(parsedState.keyContacts || {}).forEach(([jobId, data]: [string, any]) => {
          if (data.timestamp && (now - data.timestamp) < CACHE_EXPIRY) {
            cleanedContacts[parseInt(jobId)] = data
          }
        })
        
        setState({
          selectedJobId: parsedState.selectedJobId || null,
          selectedHiringManagersJobId: parsedState.selectedHiringManagersJobId || null,
          aiInsights: cleanedInsights,
          keyContacts: cleanedContacts
        })
      }
    } catch (error) {
      console.error('Error loading AI state from localStorage:', error)
      setState(defaultState)
    }
  }, [])

  // Save state to localStorage whenever it changes
  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(state))
    } catch (error) {
      console.error('Error saving AI state to localStorage:', error)
    }
  }, [state])

  // Set selected job for AI insights
  const setSelectedJobId = useCallback((jobId: number | null) => {
    setState(prev => ({ ...prev, selectedJobId: jobId }))
  }, [])

  // Set selected job for hiring managers
  const setSelectedHiringManagersJobId = useCallback((jobId: number | null) => {
    setState(prev => ({ ...prev, selectedHiringManagersJobId: jobId }))
  }, [])

  // Store AI insights data
  const storeAIInsights = useCallback((jobId: number, insights: any) => {
    setState(prev => ({
      ...prev,
      aiInsights: {
        ...prev.aiInsights,
        [jobId]: {
          jobId,
          insights,
          timestamp: Date.now()
        }
      }
    }))
  }, [])

  // Store key contacts data
  const storeKeyContacts = useCallback((jobId: number, contacts: any) => {
    setState(prev => ({
      ...prev,
      keyContacts: {
        ...prev.keyContacts,
        [jobId]: {
          jobId,
          contacts,
          timestamp: Date.now()
        }
      }
    }))
  }, [])

  // Get AI insights for a job
  const getAIInsights = useCallback((jobId: number): any | null => {
    const data = state.aiInsights[jobId]
    if (data && (Date.now() - data.timestamp) < CACHE_EXPIRY) {
      return data.insights
    }
    return null
  }, [state.aiInsights])

  // Get key contacts for a job
  const getKeyContacts = useCallback((jobId: number): any | null => {
    const data = state.keyContacts[jobId]
    if (data && (Date.now() - data.timestamp) < CACHE_EXPIRY) {
      return data.contacts
    }
    return null
  }, [state.keyContacts])

  // Clear all AI state (useful for logout or reset)
  const clearAIState = useCallback(() => {
    setState(defaultState)
    localStorage.removeItem(STORAGE_KEY)
  }, [])

  return {
    selectedJobId: state.selectedJobId,
    selectedHiringManagersJobId: state.selectedHiringManagersJobId,
    setSelectedJobId,
    setSelectedHiringManagersJobId,
    storeAIInsights,
    storeKeyContacts,
    getAIInsights,
    getKeyContacts,
    clearAIState,
    hasAIInsights: (jobId: number) => !!state.aiInsights[jobId],
    hasKeyContacts: (jobId: number) => !!state.keyContacts[jobId]
  }
}
