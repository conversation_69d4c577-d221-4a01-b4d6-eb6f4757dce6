import { useState, useEffect, useCallback, useRef } from 'react'
import { createClient } from '@/lib/supabase'
import { useAuth } from '@/contexts/AuthContext'
import { useUserProfile } from '@/contexts/UserProfileContext'

// Interface for tracked job data - compatible with UnifiedJobCard
export interface TrackedJob {
  id: number
  title: string
  company_name: string
  location?: string | null
  location_type?: string | null
  url?: string | null
  date_posted?: string | null
  category_dept?: string | null
  description?: string | null
  years_of_experience?: number | null
  companies?: {
    logo_url: string | null
  } | null
}

// Interface for the hook's return value
export interface UseTrackedJobsReturn {
  jobs: TrackedJob[]
  loading: boolean
  error: string | null
  currentPage: number
  totalPages: number
  totalJobs: number
  hasNextPage: boolean
  hasPreviousPage: boolean
  nextPage: () => void
  previousPage: () => void
  goToPage: (page: number) => void
  refresh: () => void
}

// Constants
const JOBS_PER_PAGE = 10
const CACHE_SIZE = 10 // Maximum number of pages to cache
const DEBOUNCE_DELAY = 300 // Debounce delay in milliseconds

// Cache interface for storing fetched pages
interface CacheEntry {
  jobs: TrackedJob[]
  totalJobs: number
  timestamp: number
  companiesKey: string // Key based on tracked companies to invalidate cache
}

// Simple LRU Cache implementation
class LRUCache {
  private cache = new Map<string, CacheEntry>()
  private maxSize: number

  constructor(maxSize: number) {
    this.maxSize = maxSize
  }

  get(key: string): CacheEntry | undefined {
    const entry = this.cache.get(key)
    if (entry) {
      // Move to end (most recently used)
      this.cache.delete(key)
      this.cache.set(key, entry)
    }
    return entry
  }

  set(key: string, value: CacheEntry): void {
    // Remove if already exists
    if (this.cache.has(key)) {
      this.cache.delete(key)
    }
    // Remove oldest if at capacity
    else if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value
      if (firstKey !== undefined) {
        this.cache.delete(firstKey)
      }
    }

    this.cache.set(key, value)
  }

  clear(): void {
    this.cache.clear()
  }

  // Clear entries that don't match the current companies key
  invalidateByCompaniesKey(currentKey: string): void {
    for (const [key, entry] of this.cache.entries()) {
      if (entry.companiesKey !== currentKey) {
        this.cache.delete(key)
      }
    }
  }
}

export function useTrackedJobs(): UseTrackedJobsReturn {
  const { user } = useAuth()
  const { profile } = useUserProfile()

  // State management
  const [jobs, setJobs] = useState<TrackedJob[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalJobs, setTotalJobs] = useState(0)

  // Cache and debounce refs
  const cacheRef = useRef(new LRUCache(CACHE_SIZE))
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const lastCompaniesKeyRef = useRef<string>('')
  
  // Calculate derived values
  const totalPages = Math.ceil(totalJobs / JOBS_PER_PAGE)
  const hasNextPage = currentPage < totalPages
  const hasPreviousPage = currentPage > 1

  // Helper functions for cache management
  const getCompaniesKey = useCallback(() => {
    const companies = profile?.filter_companies?.sort().join(',') || ''
    const role = profile?.filter_role?.[0] || '*'
    return `${companies}-${role}`
  }, [profile?.filter_companies, profile?.filter_role])

  const getCacheKey = useCallback((page: number, companiesKey: string) => {
    return `${companiesKey}-page-${page}`
  }, [])

  const invalidateCacheIfNeeded = useCallback(() => {
    const currentCompaniesKey = getCompaniesKey()
    if (currentCompaniesKey !== lastCompaniesKeyRef.current) {
      cacheRef.current.invalidateByCompaniesKey(currentCompaniesKey)
      lastCompaniesKeyRef.current = currentCompaniesKey
    }
  }, [getCompaniesKey])

  // Helper function to parse filter_role (handles JSON string from database)
  const getParsedRole = useCallback(() => {
    if (!profile?.filter_role) return null

    // If it's already an array, return it
    if (Array.isArray(profile.filter_role)) {
      return profile.filter_role
    }

    // If it's a JSON string, parse it
    if (typeof profile.filter_role === 'string') {
      try {
        return JSON.parse(profile.filter_role)
      } catch (error) {
        console.error('Error parsing filter_role JSON:', error)
        return null
      }
    }

    return null
  }, [profile?.filter_role])

  // Fetch jobs function with caching
  const fetchJobs = useCallback(async (page: number = 1, useCache: boolean = true) => {
    if (!user || !profile?.filter_companies || profile.filter_companies.length === 0) {
      setJobs([])
      setTotalJobs(0)
      setLoading(false)
      setError(null)
      return
    }

    const companiesKey = getCompaniesKey()
    const cacheKey = getCacheKey(page, companiesKey)

    // Check cache first (if useCache is true)
    if (useCache) {
      invalidateCacheIfNeeded()
      const cachedEntry = cacheRef.current.get(cacheKey)
      if (cachedEntry && cachedEntry.companiesKey === companiesKey) {
        // Use cached data
        setJobs(cachedEntry.jobs)
        setTotalJobs(cachedEntry.totalJobs)
        setLoading(false)
        setError(null)
        return
      }
    }

    setLoading(true)
    setError(null)

    try {
      const supabase = createClient()
      const offset = (page - 1) * JOBS_PER_PAGE
      const parsedRole = getParsedRole()

      // First, get the total count for pagination
      let countQuery = supabase
        .from('job_listings')
        .select('*', { count: 'exact', head: true })
        .in('company_name', profile.filter_companies)

      // Filter by role if not "all roles"
      if (parsedRole && parsedRole.length > 0 && parsedRole[0] !== '*') {
        countQuery = countQuery.eq('category_dept', parsedRole[0])
      }

      const { count, error: countError } = await countQuery

      if (countError) {
        throw new Error(`Failed to get job count: ${countError.message}`)
      }

      setTotalJobs(count || 0)

      // If no jobs found, set empty state
      if (!count || count === 0) {
        setJobs([])
        setLoading(false)
        return
      }

      // Fetch the actual jobs with pagination
      let jobsQuery = supabase
        .from('job_listings')
        .select(`
          id,
          title,
          company_name,
          location,
          location_type,
          url,
          date_posted,
          category_dept,
          description,
          years_of_experience
        `)
        .in('company_name', profile.filter_companies)
        .order('date_posted', { ascending: false, nullsFirst: false })
        .range(offset, offset + JOBS_PER_PAGE - 1)

      // Filter by role if not "all roles"
      if (parsedRole && parsedRole.length > 0 && parsedRole[0] !== '*') {
        jobsQuery = jobsQuery.eq('category_dept', parsedRole[0])
      }

      const { data, error: jobsError } = await jobsQuery

      if (jobsError) {
        throw new Error(`Failed to fetch jobs: ${jobsError.message}`)
      }

      // Fetch company logos separately (like other working sections)
      const uniqueCompanies = [...new Set((data || []).map(job => job.company_name))]
      let companyLogos: Record<string, string | null> = {}

      if (uniqueCompanies.length > 0) {
        const { data: logoData, error: logoError } = await supabase
          .from('companies')
          .select('name, logo_url')
          .in('name', uniqueCompanies)

        if (!logoError && logoData) {
          logoData.forEach(company => {
            companyLogos[company.name] = company.logo_url
          })
        }
      }

      // Transform the data to match our TrackedJob interface
      const transformedJobs: TrackedJob[] = (data || []).map(job => ({
        id: job.id,
        title: job.title,
        company_name: job.company_name,
        location: job.location,
        location_type: job.location_type,
        url: job.url,
        date_posted: job.date_posted,
        category_dept: job.category_dept,
        description: job.description,
        years_of_experience: job.years_of_experience,
        companies: companyLogos[job.company_name] ? { logo_url: companyLogos[job.company_name] } : null
      }))

      setJobs(transformedJobs)

      // Cache the results
      if (useCache) {
        cacheRef.current.set(cacheKey, {
          jobs: transformedJobs,
          totalJobs: count || 0,
          timestamp: Date.now(),
          companiesKey
        })
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while fetching jobs'
      setError(errorMessage)
      console.error('Error fetching tracked jobs:', err)
    } finally {
      setLoading(false)
    }
  }, [user, profile?.filter_companies, profile?.filter_role, getCompaniesKey, getCacheKey, invalidateCacheIfNeeded, getParsedRole])

  // Pagination functions
  const nextPage = useCallback(() => {
    if (hasNextPage) {
      const newPage = currentPage + 1
      setCurrentPage(newPage)
      fetchJobs(newPage)
    }
  }, [currentPage, hasNextPage, fetchJobs])

  const previousPage = useCallback(() => {
    if (hasPreviousPage) {
      const newPage = currentPage - 1
      setCurrentPage(newPage)
      fetchJobs(newPage)
    }
  }, [currentPage, hasPreviousPage, fetchJobs])

  const goToPage = useCallback((page: number) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      setCurrentPage(page)
      fetchJobs(page)
    }
  }, [currentPage, totalPages, fetchJobs])

  // Debounced refresh function
  const debouncedRefresh = useCallback((page: number = 1) => {
    // Clear existing timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current)
    }

    // Set new timeout
    debounceTimeoutRef.current = setTimeout(() => {
      fetchJobs(page, false) // Don't use cache for refresh
    }, DEBOUNCE_DELAY)
  }, [fetchJobs])

  // Refresh function
  const refresh = useCallback(() => {
    setCurrentPage(1)
    cacheRef.current.clear() // Clear cache on manual refresh
    fetchJobs(1, false) // Don't use cache for refresh
  }, [fetchJobs])

  // Effect to fetch jobs when tracked companies actually change
  useEffect(() => {
    const currentCompaniesKey = getCompaniesKey()

    // Only fetch if companies have actually changed or it's the first load
    if (currentCompaniesKey !== lastCompaniesKeyRef.current || jobs.length === 0) {
      setCurrentPage(1)
      // Use cache for normal loads, only bypass cache if companies changed
      const useCache = currentCompaniesKey === lastCompaniesKeyRef.current
      fetchJobs(1, useCache)
      lastCompaniesKeyRef.current = currentCompaniesKey
    }
  }, [getCompaniesKey, fetchJobs, jobs.length])

  // Cleanup effect
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }
    }
  }, [])

  return {
    jobs,
    loading,
    error,
    currentPage,
    totalPages,
    totalJobs,
    hasNextPage,
    hasPreviousPage,
    nextPage,
    previousPage,
    goToPage,
    refresh
  }
}
