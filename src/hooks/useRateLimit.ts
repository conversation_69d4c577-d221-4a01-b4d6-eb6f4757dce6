import { useState, useEffect } from 'react';

const DAILY_LIMIT = 50; // 50 per user per day
const STORAGE_KEY = 'ai_insights_usage';

interface UsageData {
  count: number;
  lastReset: string;
}

export function useRateLimit() {
  const [remainingCalls, setRemainingCalls] = useState<number>(DAILY_LIMIT);
  const [isLimited, setIsLimited] = useState<boolean>(false);

  useEffect(() => {
    checkAndResetDaily();
  }, []);

  const checkAndResetDaily = () => {
    const storedData = localStorage.getItem(STORAGE_KEY);
    const now = new Date();
    const today = now.toDateString();

    if (!storedData) {
      // Initialize if no data exists
      const initialData: UsageData = {
        count: 0,
        lastReset: today
      };
      localStorage.setItem(STORAGE_KEY, JSON.stringify(initialData));
      setRemainingCalls(DAILY_LIMIT);
      setIsLimited(false);
      return;
    }

    const usageData: UsageData = JSON.parse(storedData);
    
    // Reset if it's a new day
    if (usageData.lastReset !== today) {
      const resetData: UsageData = {
        count: 0,
        lastReset: today
      };
      localStorage.setItem(STORAGE_KEY, JSON.stringify(resetData));
      setRemainingCalls(DAILY_LIMIT);
      setIsLimited(false);
      return;
    }

    // Update state based on existing data
    setRemainingCalls(DAILY_LIMIT - usageData.count);
    setIsLimited(usageData.count >= DAILY_LIMIT);
  };

  const incrementUsage = () => {
    const storedData = localStorage.getItem(STORAGE_KEY);
    if (!storedData) return false;

    const usageData: UsageData = JSON.parse(storedData);
    if (usageData.count >= DAILY_LIMIT) {
      setIsLimited(true);
      return false;
    }

    const newData: UsageData = {
      count: usageData.count + 1,
      lastReset: usageData.lastReset
    };
    localStorage.setItem(STORAGE_KEY, JSON.stringify(newData));
    setRemainingCalls(DAILY_LIMIT - newData.count);
    setIsLimited(newData.count >= DAILY_LIMIT);
    return true;
  };

  return {
    remainingCalls,
    isLimited,
    incrementUsage,
    checkAndResetDaily
  };
} 