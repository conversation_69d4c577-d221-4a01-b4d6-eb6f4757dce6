import React from 'react';
import { Mail, MapPin, ExternalLink, Users, Building, Clock, Lock, Sparkles, ArrowRight } from 'lucide-react';
import Image from 'next/image';
import { CompanyLogo } from './CompanyLogo';

export const EmailJobAlertCard = () => {
  return (
    <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden max-w-md">
      {/* Email Header */}
      <div className="border-b border-gray-100 bg-gray-50/50 px-4 py-3 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="w-7 h-7 rounded-full bg-indigo-50 flex items-center justify-center">
            <Mail className="w-4 h-4 text-indigo-600" />
          </div>
          <div>
            <p className="text-sm text-gray-600">Awaloon Alerts</p>
            <p className="text-xs text-gray-500"><EMAIL></p>
          </div>
        </div>
        <span className="px-2 py-1 bg-indigo-50 text-indigo-600 text-xs font-medium rounded-full">
          🔔 New
        </span>
      </div>

      {/* Email Content */}
      <div className="p-4">
        <div className="flex items-start gap-3">
          <div className="w-10 h-10 rounded-lg bg-gray-50 border border-gray-100 flex items-center justify-center flex-shrink-0">
            <CompanyLogo
              name="OpenAI"
              logoUrl={null}
              size={24}
              className="rounded"
            />
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-gray-900 mb-1">Software Engineer</h3>
            <div className="space-y-1">
              <div className="flex items-center gap-1.5 text-gray-600">
                <Building className="w-3.5 h-3.5" />
                <span className="text-sm">OpenAI</span>
              </div>
              <div className="flex items-center gap-1.5 text-gray-600">
                <MapPin className="w-3.5 h-3.5" />
                <span className="text-sm">San Francisco, CA</span>
              </div>
              <div className="flex items-center gap-1.5 text-gray-500">
                <Clock className="w-3.5 h-3.5" />
                <span className="text-xs">20 mins ago</span>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Button */}
        <button className="mt-4 w-full bg-indigo-600 text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-indigo-700 transition-colors flex items-center justify-center gap-1.5">
          View Job
          <ExternalLink className="w-3.5 h-3.5" />
        </button>
      </div>
    </div>
  );
};

export const HiringManagerCard = () => {
  return (
    <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden max-w-sm">
      {/* Header */}
      <div className="px-4 py-3 border-b border-gray-100">
        <div className="flex items-center justify-between mb-1">
          <h3 className="font-semibold text-gray-900 text-sm">Hiring Manager</h3>
          <span className="text-xs font-medium text-indigo-600 bg-indigo-50 px-2 py-0.5 rounded-full">
            Pro
          </span>
        </div>
        <p className="text-xs text-gray-600">Get direct contact info</p>
      </div>

      {/* Contact Preview */}
      <div className="p-4">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-10 h-10 rounded-lg bg-gray-50 border border-gray-100 flex items-center justify-center flex-shrink-0">
            <CompanyLogo
              name="Notion"
              logoUrl={null}
              size={24}
              className="rounded"
            />
          </div>
          <div className="flex-1">
            <h4 className="font-medium text-gray-900 text-sm">Sarah Chen</h4>
            <p className="text-xs text-gray-600">Design Manager • Notion</p>
          </div>
        </div>

        {/* Blurred Preview */}
        <div className="relative">
          <div className="absolute inset-0 bg-white/70 backdrop-blur-[1px] z-10 flex items-center justify-center">
            <Lock className="w-4 h-4 text-gray-400" />
          </div>
          <div className="space-y-2">
            <div className="h-6 bg-gray-50 rounded" />
            <div className="h-6 bg-gray-50 rounded" />
          </div>
        </div>

        {/* CTA Button */}
        <button className="mt-4 w-full bg-indigo-50 text-indigo-600 py-2 px-3 rounded-lg text-sm font-medium hover:bg-indigo-100 transition-colors flex items-center justify-center gap-1.5">
          Unlock Contact
          <Lock className="w-3.5 h-3.5" />
        </button>
      </div>
    </div>
  );
}; 