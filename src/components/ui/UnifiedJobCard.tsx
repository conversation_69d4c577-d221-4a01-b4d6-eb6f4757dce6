'use client'

import { useState } from 'react'
import { MapPin, <PERSON>rkles, Users, ExternalLink } from 'lucide-react'
import Link from 'next/link'
import { formatDistanceToNow } from 'date-fns'
import { CompanyLogo } from '@/components/ui/CompanyLogo'
import { formatRoleForDisplay } from '@/lib/roles'
import { JobAIInsights } from '@/components/ui/JobAIInsights'
import { KeyContacts } from '@/components/ui/KeyContacts'

// Helper to always show "x time ago" and never "in x minutes"
function formatTimeAgo(dateString: string) {
  // Validate the input first
  if (!dateString || typeof dateString !== 'string' || dateString.trim() === '') {
    return null;
  }
  
  let safeString = dateString.trim();
  if (!safeString.endsWith('Z')) {
    safeString += 'Z';
  }
  
  const date = new Date(safeString);
  
  // Check if the date is valid
  if (isNaN(date.getTime())) {
    return null;
  }
  
  const now = new Date();
  const safeDate = date > now ? now : date;
  return formatDistanceToNow(safeDate, { addSuffix: true });
}

// Helper to format locations consistently
const formatLocations = (location: string | null | undefined): string => {
  if (!location || location.trim() === '') return 'Not available';
  
  try {
    const locations = JSON.parse(location);
    if (Array.isArray(locations)) {
      const validLocations = locations.filter(loc => loc && loc.trim() !== '').map(loc => 
        loc.replace(/["\[\]]/g, '').trim()
      );
      return validLocations.length > 0 ? validLocations.join(' • ') : 'Not available';
    }
    return location.replace(/["\[\]]/g, '').trim() || 'Not available';
  } catch {
    return location.replace(/["\[\]]/g, '').trim() || 'Not available';
  }
};

interface UnifiedJobCardProps {
  // Job data
  job: {
    id: number
    title: string
    company_name: string
    location?: string | null
    location_type?: string | null
    url?: string | null
    date_posted?: string | null
    category_dept?: string | null
    description?: string | null
    years_of_experience?: number | null
    companies?: {
      logo_url: string | null
    } | null
  }
  
  // Context and feature control
  context: 'dashboard' | 'marketing' | 'preview'
  isAuthenticated?: boolean
  isProUser?: boolean
  
  // Time display (for dashboard context)
  timePosted?: string // For dashboard, this could be date_sent instead of date_posted
  
  // Feature control
  showAIFeatures?: boolean
  isLimited?: boolean
  
  // Callbacks for AI features
  onAIInsightsClick?: (jobId: number) => void
  onKeyContactsClick?: (jobId: number) => void
  
  // State for expanded panels
  showingInsights?: boolean
  showingContacts?: boolean
  
  // Additional props for styling
  className?: string
  isNew?: boolean
}

export function UnifiedJobCard({
  job,
  context,
  isAuthenticated = false,
  isProUser = false,
  timePosted,
  showAIFeatures = false,
  isLimited = false,
  onAIInsightsClick,
  onKeyContactsClick,
  showingInsights = false,
  showingContacts = false,
  className = '',
  isNew = false
}: UnifiedJobCardProps) {
  
  // Determine time to display
  const displayTime = timePosted || (job.date_posted ? formatTimeAgo(job.date_posted) : null)
  
  // Show AI CTAs only if authenticated and features are enabled
  const shouldShowAICTAs = isAuthenticated && showAIFeatures
  
  return (
    <div className={`bg-white rounded-xl border border-gray-100 shadow-sm hover:shadow-md transition-all duration-200 ${className}`}>
      <div className="p-4 sm:p-6">
        <div className="flex items-start gap-3 sm:gap-4">
          <div className="relative flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12">
            <CompanyLogo
              name={job.company_name}
              logoUrl={job.companies?.logo_url || null}
              size={context === 'preview' ? 32 : 40}
            />
          </div>

          <div className="flex-1 min-w-0">
            <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-1 sm:gap-4">
              <div>
                <div className="flex items-center gap-2 flex-wrap">
                  {job.url ? (
                    <Link
                      href={job.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="hover:underline"
                    >
                      <h3 className={`font-semibold text-gray-900 ${context === 'preview' ? 'text-sm' : 'text-base sm:text-lg'}`}>
                        {job.title}
                      </h3>
                    </Link>
                  ) : (
                    <h3 className={`font-semibold text-gray-900 ${context === 'preview' ? 'text-sm' : 'text-base sm:text-lg'}`}>
                      {job.title}
                    </h3>
                  )}
                  {isNew && (
                    <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                      New
                    </span>
                  )}
                  {context === 'preview' && (
                    <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      Example
                    </span>
                  )}
                </div>
                <div className="mt-1 flex items-center gap-2">
                  <span className={`font-medium text-gray-700 ${context === 'preview' ? 'text-xs' : 'text-sm'}`}>
                    {job.company_name}
                  </span>
                  {displayTime && (
                    <span className={`hidden sm:inline text-gray-400 ${context === 'preview' ? 'text-xs' : 'text-xs'}`}>
                      • {displayTime}
                    </span>
                  )}
                </div>
              </div>
              {displayTime && (
                <span className={`sm:hidden text-gray-400 ${context === 'preview' ? 'text-xs' : 'text-xs'}`}>
                  {displayTime}
                </span>
              )}
            </div>

            {/* Job Metadata - Hidden on mobile for compact view */}
            {context !== 'preview' && (
              <div className="hidden sm:flex items-center gap-2 mt-3 text-xs text-gray-500">
                <span className="inline-flex items-center gap-1.5">
                  <MapPin className="h-3.5 w-3.5 text-gray-400" />
                  {formatLocations(job.location)}
                </span>
                <span className="text-gray-300">•</span>
                <span>{formatRoleForDisplay(job.category_dept || '')}</span>
                {job.location_type && (
                  <>
                    <span className="text-gray-300">•</span>
                    <span>{job.location_type}</span>
                  </>
                )}
                {job.years_of_experience !== null && 
                 job.years_of_experience !== undefined && 
                 job.years_of_experience !== -1 && (
                  <>
                    <span className="text-gray-300">•</span>
                    <span>{Number(job.years_of_experience).toFixed(1)}+ years</span>
                  </>
                )}
              </div>
            )}

            {/* Mobile-only essential metadata */}
            {context !== 'preview' && (
              <div className="sm:hidden flex items-center gap-2 mt-2 text-sm text-gray-600">
                <span className="inline-flex items-center gap-1">
                  <MapPin className="h-4 w-4 text-gray-400" />
                  {formatLocations(job.location)}
                </span>
              </div>
            )}

            {/* Simplified metadata for preview context */}
            {context === 'preview' && (
              <div className="flex items-center gap-2 mt-2 text-sm text-gray-600">
                <span className="inline-flex items-center gap-1">
                  <MapPin className="h-3 w-3 text-gray-400" />
                  {formatLocations(job.location)}
                </span>
              </div>
            )}

            {/* Action Buttons - Only show for dashboard context with authenticated users */}
            {shouldShowAICTAs && context === 'dashboard' && (
              <div className="mt-4 flex flex-col sm:flex-row gap-2.5">
                {/* AI Insights Button */}
                <button
                  onClick={() => onAIInsightsClick?.(job.id)}
                  className={`w-full sm:w-auto px-4 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 flex items-center justify-center gap-2
                    ${isLimited 
                      ? 'bg-gray-50 text-gray-400 cursor-not-allowed'
                      : 'bg-gradient-to-r from-blue-50 to-blue-100 text-blue-700 hover:from-blue-100 hover:to-blue-200 border border-blue-100'
                    }`}
                  disabled={isLimited}
                >
                  <Sparkles className="h-4 w-4" />
                  {showingInsights ? 'Hide Insights' : 'Get AI Insights'}
                  {isLimited && <span className="ml-1">(Daily limit reached)</span>}
                </button>

                {/* Key Contacts Button */}
                <button
                  onClick={() => onKeyContactsClick?.(job.id)}
                  className={`w-full sm:w-auto px-4 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 flex items-center justify-center gap-2
                    ${isLimited 
                      ? 'bg-gray-50 text-gray-400 cursor-not-allowed'
                      : 'bg-gradient-to-r from-blue-50 to-blue-100 text-blue-700 hover:from-blue-100 hover:to-blue-200 border border-blue-100'
                    }`}
                  disabled={isLimited}
                >
                  <Users className="h-4 w-4" />
                  {showingContacts ? 'Hide Contacts' : 'Find Key Contacts'}
                  {isLimited && <span className="ml-1">(Daily limit reached)</span>}
                </button>
              </div>
            )}

            {/* View Job CTA for marketing context */}
            {context === 'marketing' && job.url && (
              <div className="mt-4">
                <Link
                  href={job.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-1.5 px-4 py-2 text-sm bg-indigo-50 text-indigo-600 rounded-lg hover:bg-indigo-100 transition-colors"
                >
                  View Job
                  <ExternalLink className="w-3.5 h-3.5" />
                </Link>
              </div>
            )}

            {/* AI Insights Panel */}
            {showingInsights && context === 'dashboard' && (
              <div className="mt-4 sm:mt-6 px-3 sm:px-4 py-4 bg-gray-50 rounded-lg">
                <JobAIInsights
                  jobTitle={job.title}
                  companyName={job.company_name}
                  description={job.description || undefined}
                  yearsOfExperience={job.years_of_experience || undefined}
                  department={formatRoleForDisplay(job.category_dept || '')}
                />
              </div>
            )}

            {/* Key Contacts Panel */}
            {showingContacts && context === 'dashboard' && (
              <div className="mt-4 sm:mt-6 px-3 sm:px-4 py-4 bg-gray-50 rounded-lg">
                <KeyContacts
                  jobId={job.id.toString()}
                  jobTitle={job.title}
                  companyName={job.company_name}
                  department={formatRoleForDisplay(job.category_dept || '')}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
} 