'use client'

import React from 'react'
import { useUserProfile } from '@/contexts/UserProfileContext'
import { Sparkles } from 'lucide-react'
import { Button } from './button'

interface FeatureGateProps {
  children: React.ReactNode
  feature: string
  fallback?: React.ReactNode
}

export function FeatureGate({ children, feature, fallback }: FeatureGateProps) {
  const { isProUser, loading } = useUserProfile()

  if (loading) {
    return null // Don't show anything while loading to prevent flicker
  }

  if (isProUser) {
    return <>{children}</>
  }

  if (fallback) {
    return <>{fallback}</>
  }

  return (
    <div className="rounded-lg border border-gray-200 p-4 bg-gray-50">
      <div className="flex items-start gap-4">
        <div className="flex-grow">
          <div className="flex items-center gap-2 mb-2">
            <Sparkles className="w-4 h-4 text-indigo-500" />
            <h3 className="font-medium text-gray-900">Pro Feature: {feature}</h3>
          </div>
          <p className="text-sm text-gray-600 mb-4">
            Upgrade to Pro to access this feature and many more.
          </p>
          <Button
            variant="default"
            className="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 text-white"
            onClick={() => window.location.href = '/dashboard/settings'}
          >
            Upgrade to Pro
          </Button>
        </div>
      </div>
    </div>
  )
} 