'use client'

import React, { useState, useEffect } from 'react';

export function LogoTest() {
  const [envKeyTest, setEnvKeyTest] = useState<string>('');
  const [tests, setTests] = useState({
    hardcoded: false,
    envVariable: false,
    constructedUrl: false
  });

  // Test 1: Hardcoded URL (known working)
  const hardcodedUrl = "https://img.logo.dev/google.com?token=pk_Yg99lapTRZuQWmUYC-Z3iw";
  
  // Test 2: Environment Variable Test
  const envUrl = `https://img.logo.dev/google.com?token=${process.env.NEXT_PUBLIC_LOGO_DEV_PUBLIC_KEY}`;
  
  // Test 3: Constructed URL
  const constructUrl = (domain: string) => 
    `https://img.logo.dev/${domain}?token=${process.env.NEXT_PUBLIC_LOGO_DEV_PUBLIC_KEY}`;

  useEffect(() => {
    setEnvKeyTest(process.env.NEXT_PUBLIC_LOGO_DEV_PUBLIC_KEY || 'Not found');
    
    // Debug: Log detailed comparison
    console.log('Hardcoded token:', 'pk_Yg99lapTRZuQWmUYC-Z3iw');
    console.log('Env token:', process.env.NEXT_PUBLIC_LOGO_DEV_PUBLIC_KEY);
  }, []);

  const TestImage = ({ src, label }: { src: string, label: string }) => (
    <div className="space-y-2">
      <p className="text-xs text-gray-500">{label}</p>
      <div className="relative w-[100px] h-[50px] bg-gray-50 rounded-lg p-2">
        <img
          src={src}
          alt={`Test ${label}`}
          className="w-full h-full object-contain"
          onError={() => setTests(prev => ({ ...prev, [label.toLowerCase()]: false }))}
          onLoad={() => setTests(prev => ({ ...prev, [label.toLowerCase()]: true }))}
        />
      </div>
    </div>
  );

  // Helper function to show character codes
  const getCharCodes = (str: string) => 
    Array.from(str).map(char => char.charCodeAt(0).toString(16)).join(' ');

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-3 gap-4">
        <TestImage src={hardcodedUrl} label="hardcoded" />
        <TestImage src={envUrl} label="envVariable" />
        <TestImage src={constructUrl('google.com')} label="constructedUrl" />
      </div>

      {/* Enhanced Debug Panel */}
      <div className="p-4 bg-gray-50 rounded-lg space-y-2">
        <p className="text-sm font-medium text-gray-900">Debug Information:</p>
        
        <div className="text-xs text-gray-500 space-y-1">
          <p>Environment Variable: {envKeyTest}</p>
          <p>Environment Variable Length: {envKeyTest.length}</p>
          <p>Hardcoded Token Length: {"pk_Yg99lapTRZuQWmUYC-Z3iw".length}</p>
          
          <p className="font-medium mt-2">Character Codes:</p>
          <p>Hardcoded: {getCharCodes("pk_Yg99lapTRZuQWmUYC-Z3iw")}</p>
          <p>Env Variable: {getCharCodes(envKeyTest)}</p>

          <p className="mt-2">Test Results:</p>
          <ul className="list-disc pl-4">
            <li>Hardcoded URL: {tests.hardcoded ? '✅' : '❌'}</li>
            <li>Env Variable URL: {tests.envVariable ? '✅' : '❌'}</li>
            <li>Constructed URL: {tests.constructedUrl ? '✅' : '❌'}</li>
          </ul>

          <p className="mt-2 font-medium">URLs being tested:</p>
          <div className="space-y-1 break-all">
            <p>1. {hardcodedUrl}</p>
            <p>2. {envUrl}</p>
            <p>3. {constructUrl('google.com')}</p>
          </div>
        </div>
      </div>
    </div>
  );
}