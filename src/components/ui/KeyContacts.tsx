"use client";

import React from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

// Loading state messages that will cycle
const LOADING_MESSAGES = [
  "Scanning LinkedIn for the perfect contacts... 🔍",
  "Analyzing professional profiles... 👀",
  "Found someone interesting... let's check their role 📋",
  "Getting closer to the right matches... ⭐",
  "Almost there! Handpicking the best contacts... ✨"
];

interface Contact {
  name: string;
  title: string;
  url: string;
  relevanceLevel: 'Super Relevant' | 'Relevant' | 'Somewhat Relevant';
  recommendationReason: string;
}

interface ContactResponse {
  success: boolean;
  error?: string;
  message?: string;
  results: Array<{
    url: string;
    summary: {
      name: string;
      title: string;
      relevanceLevel: 'Super Relevant' | 'Relevant' | 'Somewhat Relevant';
      recommendationReason: string;
    }
  }>;
}

interface KeyContactsProps {
  jobId: string;
  jobTitle: string;
  companyName: string;
  department: string;
}

const MAX_RETRIES = 3;
const RETRY_DELAY = 3000; // 3 seconds
const MESSAGE_CHANGE_INTERVAL = 3000; // Changed from 2000 to 3000 (3 seconds per message)

export function KeyContacts({ jobId, jobTitle, companyName, department }: KeyContactsProps) {
  const [isLoading, setIsLoading] = React.useState(false);
  const [isInitialLoad, setIsInitialLoad] = React.useState(true);
  const [contacts, setContacts] = React.useState<Contact[]>([]);
  const [error, setError] = React.useState<string | null>(null);
  const [retryCount, setRetryCount] = React.useState(0);
  const [shouldShowError, setShouldShowError] = React.useState(false);
  const [currentMessageIndex, setCurrentMessageIndex] = React.useState(0);

  // Cycle through loading messages
  React.useEffect(() => {
    if (isLoading || (retryCount > 0 && !shouldShowError)) {
      const interval = setInterval(() => {
        setCurrentMessageIndex((prev) => (prev + 1) % LOADING_MESSAGES.length);
      }, MESSAGE_CHANGE_INTERVAL);
      
      return () => clearInterval(interval);
    }
  }, [isLoading, retryCount, shouldShowError]);

  const fetchContacts = React.useCallback(async () => {
    if (retryCount >= MAX_RETRIES) {
      setShouldShowError(true);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/key-contacts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          jobId,
          jobTitle,
          companyName,
          department,
        }),
      });

      const data = await response.json() as ContactResponse;
      console.log('Response data:', data);

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch contacts. Please try again.');
      }

      if (data.success && Array.isArray(data.results)) {
        const formattedContacts: Contact[] = data.results.map(result => ({
          name: result.summary.name,
          title: result.summary.title,
          url: result.url,
          relevanceLevel: result.summary.relevanceLevel,
          recommendationReason: result.summary.recommendationReason,
        }));

        console.log('Formatted contacts:', formattedContacts);
        
        if (formattedContacts.length > 0) {
          setContacts(formattedContacts);
          setError(null);
          setShouldShowError(false);
          setRetryCount(0);
        } else {
          setError('No relevant contacts found for this position yet. We\'ll keep searching.');
          setRetryCount(prev => prev + 1);
        }
      } else {
        console.log('Invalid or empty results:', data);
        setError('Unable to find relevant contacts at this time. Please try again later.');
        setRetryCount(prev => prev + 1);
      }
    } catch (err) {
      console.error('Error:', err);
      const errorMessage = err instanceof Error ? err.message : 'Error fetching contacts';
      setError(errorMessage);
      setRetryCount(prev => prev + 1);
    } finally {
      setIsLoading(false);
      setIsInitialLoad(false);
    }
  }, [jobId, jobTitle, companyName, department, retryCount]);

  React.useEffect(() => {
    if (retryCount > 0 && retryCount < MAX_RETRIES && !contacts.length) {
      const timer = setTimeout(() => {
        fetchContacts();
      }, RETRY_DELAY);
      
      return () => clearTimeout(timer);
    }
    
    if (retryCount >= MAX_RETRIES && !contacts.length) {
      setShouldShowError(true);
    }
  }, [retryCount, contacts.length, fetchContacts]);

  React.useEffect(() => {
    fetchContacts();
  }, [fetchContacts]);

  return (
    <div className="w-full space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Key Contacts</h3>
      </div>

      {(isLoading || (retryCount > 0 && !shouldShowError)) && (
        <div className="space-y-6">
          {/* Animated loading message */}
          <div className="text-center py-4">
            <p 
              key={currentMessageIndex} 
              className="text-blue-600 font-medium animate-fadeInUp"
              style={{
                animation: 'fadeInUp 0.5s ease-out'
              }}
            >
              {LOADING_MESSAGES[currentMessageIndex]}
            </p>
          </div>

          {/* Profile skeleton loaders */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[...Array(3)].map((_, i) => (
              <Card 
                key={i} 
                className="p-4 animate-pulse-subtle"
                style={{
                  animation: `pulseAndSlide 2s ease-in-out ${i * 0.3}s infinite`
                }}
              >
                <div className="flex items-center space-x-4">
                  <div className="h-12 w-12 rounded-full bg-gray-200 animate-pulse" />
                  <div className="space-y-2 flex-1">
                    <div className="h-4 bg-gray-200 rounded w-3/4 animate-pulse" />
                    <div className="h-4 bg-gray-200 rounded w-1/2 animate-pulse" />
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}

      {shouldShowError && error && !isLoading && contacts.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <p>{error}</p>
          {retryCount >= MAX_RETRIES && (
            <Button
              onClick={() => {
                setRetryCount(0);
                setShouldShowError(false);
                fetchContacts();
              }}
              className="mt-4"
              variant="outline"
            >
              Try Again
            </Button>
          )}
        </div>
      )}

      {!isLoading && contacts.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {contacts.map((contact, index) => (
            <a
              key={index}
              href={contact.url}
              target="_blank"
              rel="noopener noreferrer"
              className="block transition-transform hover:scale-[1.02]"
            >
              <Card className="relative overflow-hidden">
                <div className={`w-full py-1.5 text-center text-sm font-medium ${
                  contact.relevanceLevel === 'Super Relevant' 
                    ? 'bg-green-100 text-green-800'
                    : contact.relevanceLevel === 'Relevant'
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {contact.relevanceLevel}
                </div>

                <div className="p-6">
                  <div className="flex items-center gap-4 mb-6">
                    <div className="flex-shrink-0">
                      <Image
                        src="/avatar-placeholder.svg"
                        alt={contact.name || 'Profile picture'}
                        width={48}
                        height={48}
                        className="rounded-full border-2 border-gray-100"
                      />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">
                        {contact.name || 'Unknown Name'}
                      </h4>
                      <p className="text-sm text-gray-600">
                        {contact.title || 'Unknown Title'}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h5 className="font-medium text-sm text-gray-900">
                      {`Why they're relevant`}
                    </h5>
                    <p className="text-sm text-gray-600">
                      {contact.recommendationReason}
                    </p>
                  </div>

                  <div className="mt-6 pt-4 border-t border-gray-100">
                    <span className="text-sm font-medium text-blue-600 hover:text-blue-700 inline-flex items-center">
                      View Profile
                      <svg className="w-4 h-4 ml-1" viewBox="0 0 16 16" fill="none">
                        <path d="M6.5 3.5L11 8L6.5 12.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </span>
                  </div>
                </div>
              </Card>
            </a>
          ))}
        </div>
      )}

      <style jsx global>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes pulseAndSlide {
          0% {
            transform: translateX(-5px);
            opacity: 0.6;
          }
          50% {
            transform: translateX(0);
            opacity: 1;
          }
          100% {
            transform: translateX(-5px);
            opacity: 0.6;
          }
        }

        .animate-pulse-subtle {
          animation: pulseAndSlide 2s ease-in-out infinite;
        }
      `}</style>
    </div>
  );
} 