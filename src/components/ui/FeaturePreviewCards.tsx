import React from 'react';
import { Mail, MapPin, ExternalLink, Users, Building, Clock, Lock, Sparkles, ArrowRight } from 'lucide-react';
import Image from 'next/image';
import { CompanyLogo } from './CompanyLogo';

export const JobAlertEmailPreview = () => {
  return (
    <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
      {/* Email Header */}
      <div className="border-b border-gray-100 bg-gray-50/50 px-5 py-3 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 rounded-full bg-indigo-50 flex items-center justify-center">
            <Mail className="w-4 h-4 text-indigo-600" />
          </div>
          <div>
            <p className="text-sm text-gray-600">From: Awaloon Alerts</p>
            <p className="text-xs text-gray-500"><EMAIL></p>
          </div>
        </div>
        <span className="px-2.5 py-1 bg-indigo-50 text-indigo-600 text-xs font-medium rounded-full flex items-center gap-1">
          🔔 Job Alert
        </span>
      </div>

      {/* Email Content */}
      <div className="p-5">
        <div className="flex items-start gap-4">
          <div className="w-12 h-12 rounded-lg bg-gray-50 border border-gray-100 flex items-center justify-center flex-shrink-0">
            <CompanyLogo
              name="OpenAI"
              logoUrl={null}
              size={32}
              className="rounded"
            />
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-gray-900 mb-1">Software Engineer</h3>
            <div className="space-y-1.5">
              <div className="flex items-center gap-2 text-gray-600">
                <Building className="w-4 h-4" />
                <span>OpenAI</span>
              </div>
              <div className="flex items-center gap-2 text-gray-600">
                <MapPin className="w-4 h-4" />
                <span>San Francisco, CA</span>
              </div>
              <div className="flex items-center gap-2 text-gray-500">
                <Clock className="w-4 h-4" />
                <span className="text-sm">20 mins ago</span>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Button */}
        <button className="mt-5 w-full bg-indigo-600 text-white py-2.5 px-4 rounded-lg font-medium hover:bg-indigo-700 transition-colors flex items-center justify-center gap-2">
          View Job
          <ExternalLink className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
};

export const ProFeaturePreview = () => {
  return (
    <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
      {/* Header */}
      <div className="px-5 py-4 border-b border-gray-100">
        <div className="flex items-center justify-between mb-1">
          <h3 className="font-semibold text-gray-900">Key Contacts</h3>
          <span className="text-xs font-medium text-indigo-600 bg-indigo-50 px-2 py-1 rounded-full">
            Pro Feature
          </span>
        </div>
        <p className="text-sm text-gray-600">Unlock direct access to the hiring team</p>
      </div>

      {/* Contact Preview */}
      <div className="p-5">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center flex-shrink-0 font-medium text-gray-600">
            AC
          </div>
          <div className="flex-1">
            <h4 className="font-medium text-gray-900">Alex Chen</h4>
            <p className="text-sm text-gray-600">Design Manager • Notion</p>
          </div>
          <button className="flex items-center justify-center w-8 h-8 rounded-full text-gray-400" disabled>
            <ArrowRight className="w-4 h-4" />
          </button>
        </div>

        {/* AI Insights Preview */}
        <div className="bg-gray-50 rounded-xl p-4 mb-5">
          <div className="flex items-center gap-2 mb-3">
            <Sparkles className="w-5 h-5 text-indigo-600" />
            <span className="font-medium text-gray-900">AI Interview Insights</span>
          </div>
          <div className="text-sm text-gray-600">
            5 Interview Questions Generated 🤖
          </div>
        </div>

        {/* Blurred Preview */}
        <div className="relative">
          <div className="absolute inset-0 bg-white/80 backdrop-blur-[2px] z-10 flex items-center justify-center">
            <Lock className="w-5 h-5 text-gray-400" />
          </div>
          <div className="space-y-3">
            <div className="h-8 bg-gray-50 rounded-lg" />
            <div className="h-8 bg-gray-50 rounded-lg" />
          </div>
        </div>

        {/* CTA Button */}
        <button className="mt-5 w-full bg-indigo-50 text-indigo-600 py-2.5 px-4 rounded-lg font-medium hover:bg-indigo-100 transition-colors flex items-center justify-center gap-2">
          Upgrade to Unlock
          <Lock className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
};

export const EmailExamplePreview = () => {
  return (
    <div className="bg-white rounded-2xl shadow-xl border border-gray-100 max-w-xl mx-auto overflow-hidden relative">
      {/* Preview badge */}
      <span className="absolute top-5 right-5 px-4 py-1 bg-indigo-50 text-indigo-600 text-sm font-medium rounded-full">Preview</span>
      {/* Email Header */}
      <div className="flex items-center gap-3 px-6 pt-6 pb-2">
        <div className="relative">
          <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-400 rounded-full border-2 border-white" />
          <div className="w-9 h-9 rounded-full bg-indigo-50 flex items-center justify-center">
            <Mail className="w-5 h-5 text-indigo-600" />
          </div>
        </div>
        <div>
          <p className="text-gray-700 font-medium text-base leading-tight">From: Awaloon Alerts</p>
          <p className="text-gray-500 text-sm leading-tight"><EMAIL></p>
        </div>
      </div>
      {/* Subject */}
      <div className="px-6 pt-2 pb-1">
        <p className="font-bold text-lg text-gray-900 flex items-center gap-2">
          <span>🚀</span> New Alert: <span className="ml-1">Product Manager at OpenAI</span>
        </p>
      </div>
      {/* Job Info */}
      <div className="px-6 pt-2 flex items-center gap-3">
        <CompanyLogo
          name="OpenAI"
          logoUrl={null}
          size={32}
          className="rounded"
        />
        <div>
          <p className="font-semibold text-gray-900 text-base leading-tight">Product Manager</p>
          <p className="text-gray-600 text-sm leading-tight">OpenAI • San Francisco, CA</p>
        </div>
      </div>
      {/* Posted Time */}
      <div className="px-6 pt-2 pb-1">
        <p className="text-gray-500 text-sm">Posted 1 hour ago</p>
      </div>
      {/* Button */}
      <div className="bg-gray-50 px-6 py-4 mt-3">
        <button className="w-full text-gray-700 font-medium text-base py-2 rounded-lg bg-transparent hover:bg-gray-100 transition">
          View Job Details
        </button>
      </div>
    </div>
  );
}; 