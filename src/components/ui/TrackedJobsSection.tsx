'use client'

import React, { useState } from 'react'
import { useTrackedJobs } from '@/hooks/useTrackedJobs'
import { UnifiedJobCard } from './UnifiedJobCard'
import { Skeleton } from './skeleton'
import { Button } from './button'
import { RefreshCw, Building2, Search, AlertCircle } from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { useUserProfile } from '@/contexts/UserProfileContext'
import { useRateLimit } from '@/hooks/useRateLimit'
import { UpgradeToProModal } from './UpgradeToProModal'
import { toast } from 'react-hot-toast'
import { usePersistentAIState } from '@/hooks/usePersistentAIState'

interface TrackedJobsSectionProps {
  className?: string
}

// Loading skeleton component for job cards
function JobCardSkeleton() {
  return (
    <div className="bg-white rounded-xl border border-gray-100 shadow-sm p-4 sm:p-6">
      <div className="flex items-start gap-3 sm:gap-4">
        <Skeleton className="w-10 h-10 sm:w-12 sm:h-12 rounded-lg" />
        <div className="flex-1 space-y-3">
          <div className="space-y-2">
            <Skeleton className="h-5 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
          <div className="flex items-center gap-4">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-24" />
          </div>
        </div>
      </div>
    </div>
  )
}

// Empty state component
function EmptyState({ 
  type, 
  onRefresh 
}: { 
  type: 'no-companies' | 'no-jobs' | 'error'
  onRefresh?: () => void 
}) {
  const getEmptyStateContent = () => {
    switch (type) {
      case 'no-companies':
        return {
          icon: Building2,
          title: 'No tracked companies',
          description: 'Start tracking companies to see job opportunities here',
          action: null
        }
      case 'no-jobs':
        return {
          icon: Search,
          title: 'No recent jobs',
          description: 'No recent jobs from your tracked companies. Check back soon!',
          action: onRefresh ? (
            <Button 
              variant="outline" 
              onClick={onRefresh}
              className="mt-4"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
          ) : null
        }
      case 'error':
        return {
          icon: AlertCircle,
          title: 'Unable to load jobs',
          description: 'Please try again.',
          action: onRefresh ? (
            <Button 
              variant="outline" 
              onClick={onRefresh}
              className="mt-4"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Try Again
            </Button>
          ) : null
        }
    }
  }

  const { icon: Icon, title, description, action } = getEmptyStateContent()

  return (
    <div className="bg-white rounded-xl border border-gray-200 p-8 text-center">
      <div className="flex flex-col items-center space-y-4">
        <div className="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center">
          <Icon className="w-6 h-6 text-gray-400" />
        </div>
        <div className="space-y-2">
          <h3 className="text-lg font-medium text-gray-900">{title}</h3>
          <p className="text-sm text-gray-500 max-w-sm">{description}</p>
        </div>
        {action}
      </div>
    </div>
  )
}

// Enhanced Pagination component with numbered pages
function PaginationControls({
  currentPage,
  totalPages,
  hasNextPage,
  hasPreviousPage,
  onNextPage,
  onPreviousPage,
  onPageChange,
  loading,
  totalJobs
}: {
  currentPage: number
  totalPages: number
  hasNextPage: boolean
  hasPreviousPage: boolean
  onNextPage: () => void
  onPreviousPage: () => void
  onPageChange: (page: number) => void
  loading: boolean
  totalJobs: number
}) {
  if (totalPages <= 1) return null

  // Calculate which page numbers to show (max 5 pages)
  const getPageNumbers = () => {
    const pages = []
    if (totalPages <= 5) {
      // Show all pages if 5 or fewer
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i)
      }
    } else if (currentPage <= 3) {
      // Show first 5 pages if current page is near the beginning
      for (let i = 1; i <= 5; i++) {
        pages.push(i)
      }
    } else if (currentPage >= totalPages - 2) {
      // Show last 5 pages if current page is near the end
      for (let i = totalPages - 4; i <= totalPages; i++) {
        pages.push(i)
      }
    } else {
      // Show current page and 2 pages on each side
      for (let i = currentPage - 2; i <= currentPage + 2; i++) {
        pages.push(i)
      }
    }
    return pages
  }

  const pageNumbers = getPageNumbers()
  const startJob = (currentPage - 1) * 10 + 1
  const endJob = Math.min(currentPage * 10, totalJobs)

  return (
    <div className="pt-6 border-t border-gray-100">
      {/* Job count info */}
      <div className="flex items-center justify-between mb-4">
        <div className="text-sm text-gray-500">
          Showing {startJob}-{endJob} of {totalJobs} jobs
        </div>
        <div className="text-sm text-gray-500">
          Page {currentPage} of {totalPages}
        </div>
      </div>

      {/* Pagination controls */}
      <div className="flex items-center justify-center gap-2">
        {/* Previous button */}
        <Button
          variant="outline"
          size="sm"
          onClick={onPreviousPage}
          disabled={!hasPreviousPage || loading}
          className="px-3 py-2"
        >
          Previous
        </Button>

        {/* Page number buttons */}
        <div className="flex items-center gap-1">
          {pageNumbers.map((pageNum) => (
            <button
              key={pageNum}
              onClick={() => onPageChange(pageNum)}
              disabled={loading}
              className={`w-8 h-8 text-sm font-medium rounded transition-all duration-200 ${
                currentPage === pageNum
                  ? 'bg-indigo-600 text-white shadow-sm'
                  : 'bg-white text-gray-700 border border-gray-200 hover:bg-gray-50 hover:border-gray-300'
              } ${loading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
              aria-label={`Go to page ${pageNum}`}
              aria-current={currentPage === pageNum ? 'page' : undefined}
            >
              {pageNum}
            </button>
          ))}
        </div>

        {/* Next button */}
        <Button
          variant="outline"
          size="sm"
          onClick={onNextPage}
          disabled={!hasNextPage || loading}
          className="px-3 py-2"
        >
          Next
        </Button>
      </div>

      {/* Loading indicator */}
      {loading && (
        <div className="flex items-center justify-center mt-3">
          <div className="flex items-center gap-2 text-sm text-gray-500">
            <RefreshCw className="w-4 h-4 animate-spin" />
            Loading...
          </div>
        </div>
      )}
    </div>
  )
}

export function TrackedJobsSection({ className = '' }: TrackedJobsSectionProps) {
  const { user } = useAuth()
  const { profile, isProUser } = useUserProfile()
  const { remainingCalls, isLimited, incrementUsage } = useRateLimit()

  // Persistent AI state
  const {
    selectedJobId,
    selectedHiringManagersJobId,
    setSelectedJobId,
    setSelectedHiringManagersJobId,
    storeAIInsights,
    storeKeyContacts,
    getAIInsights,
    getKeyContacts,
    hasAIInsights,
    hasKeyContacts
  } = usePersistentAIState()

  // Local state for modals
  const [upgradeModalOpen, setUpgradeModalOpen] = useState(false)
  const [upgradeFeature, setUpgradeFeature] = useState<'insights' | 'contacts' | null>(null)

  const {
    jobs,
    loading,
    error,
    currentPage,
    totalPages,
    totalJobs,
    hasNextPage,
    hasPreviousPage,
    nextPage,
    previousPage,
    goToPage,
    refresh
  } = useTrackedJobs()

  // AI feature handlers (same logic as job hunt page)
  const handleAIInsightsClick = (jobId: number) => {
    if (!isProUser) {
      setUpgradeFeature('insights')
      setUpgradeModalOpen(true)
      return
    }
    if (selectedJobId === jobId) {
      setSelectedJobId(null);
      return;
    }
    if (isLimited) {
      toast.error('You have reached your daily limit for AI Insights. Please try again tomorrow.');
      return;
    }
    if (incrementUsage()) {
      setSelectedJobId(jobId);
    } else {
      toast.error('You have reached your daily limit for AI Insights. Please try again tomorrow.');
    }
  };

  const handleHiringManagersClick = (jobId: number) => {
    if (!isProUser) {
      setUpgradeFeature('contacts')
      setUpgradeModalOpen(true)
      return
    }
    if (selectedHiringManagersJobId === jobId) {
      setSelectedHiringManagersJobId(null);
      return;
    }
    if (isLimited) {
      toast.error('You have reached your daily limit for AI features. Please try again tomorrow.');
      return;
    }
    if (incrementUsage()) {
      setSelectedHiringManagersJobId(jobId);
    } else {
      toast.error('You have reached your daily limit for AI features. Please try again tomorrow.');
    }
  };

  // Don't render if user is not authenticated
  if (!user) return null

  // Determine what to show
  const hasTrackedCompanies = profile?.filter_companies && profile.filter_companies.length > 0
  const hasJobs = jobs.length > 0
  const showEmptyState = !loading && (!hasTrackedCompanies || (!hasJobs && !error))
  const showError = !loading && error
  const showJobs = !loading && hasJobs && !error

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Section Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">
            Recent Jobs from Your Tracked Companies
          </h2>
          <p className="text-sm text-gray-500 mt-1">
            {loading ? (
              'Loading jobs...'
            ) : hasTrackedCompanies ? (
              totalJobs > 0 ? (
                `${totalJobs} job${totalJobs === 1 ? '' : 's'} from ${profile.filter_companies.length} tracked compan${profile.filter_companies.length === 1 ? 'y' : 'ies'}`
              ) : (
                `Tracking ${profile.filter_companies.length} compan${profile.filter_companies.length === 1 ? 'y' : 'ies'}`
              )
            ) : (
              'Track companies to see job opportunities'
            )}
          </p>
        </div>
        
        {hasTrackedCompanies && !loading && (
          <Button
            variant="outline"
            size="sm"
            onClick={refresh}
            disabled={loading}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        )}
      </div>

      {/* Content */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        {/* Loading State */}
        {loading && (
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, index) => (
              <JobCardSkeleton key={index} />
            ))}
          </div>
        )}

        {/* Error State */}
        {showError && (
          <EmptyState type="error" onRefresh={refresh} />
        )}

        {/* Empty States */}
        {showEmptyState && (
          <EmptyState 
            type={!hasTrackedCompanies ? 'no-companies' : 'no-jobs'} 
            onRefresh={hasTrackedCompanies ? refresh : undefined}
          />
        )}

        {/* Jobs List */}
        {showJobs && (
          <div className="space-y-4">
            {jobs.map((job) => {
              // Check if job is new (posted within last 24 hours)
              const isNew = Boolean(job.date_posted &&
                new Date().getTime() - new Date(job.date_posted).getTime() < 24 * 60 * 60 * 1000);

              return (
                <UnifiedJobCard
                  key={job.id}
                  job={job}
                  context="dashboard"
                  isAuthenticated={true}
                  isProUser={isProUser}
                  showAIFeatures={true}
                  isLimited={isLimited}
                  onAIInsightsClick={handleAIInsightsClick}
                  onKeyContactsClick={handleHiringManagersClick}
                  showingInsights={selectedJobId === job.id}
                  showingContacts={selectedHiringManagersJobId === job.id}
                  isNew={isNew}
                  className="border-0 shadow-none bg-gray-50 hover:bg-gray-100"
                />
              );
            })}
            
            {/* Pagination */}
            <PaginationControls
              currentPage={currentPage}
              totalPages={totalPages}
              hasNextPage={hasNextPage}
              hasPreviousPage={hasPreviousPage}
              onNextPage={nextPage}
              onPreviousPage={previousPage}
              onPageChange={goToPage}
              loading={loading}
              totalJobs={totalJobs}
            />
          </div>
        )}
      </div>

      {/* Upgrade Modal */}
      <UpgradeToProModal
        open={upgradeModalOpen}
        onOpenChange={setUpgradeModalOpen}
        userId={user?.id || ''}
      />
    </div>
  )
}
