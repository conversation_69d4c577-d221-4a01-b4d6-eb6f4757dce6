import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from './dialog';
import { Spark<PERSON>, Users, Bell } from 'lucide-react';
import { Button } from './button';
import { createClient } from '@/lib/supabase';

interface UpgradeToProModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  userId: string;
}

export function UpgradeToProModal({ open, onOpenChange, userId }: UpgradeToProModalProps) {
  const [isLoading, setIsLoading] = useState(false);

  // Handles redirecting to Stripe checkout
  const handleUpgrade = async () => {
    try {
      setIsLoading(true);
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        throw new Error('No auth session found');
      }
      const response = await fetch('/api/stripe/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify({ user_id: userId }),
      });
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.error || 'Failed to create checkout session');
      }
      window.location.href = data.url;
    } catch (error) {
      setIsLoading(false);
      // Optionally show a toast or error message
      console.error('Error:', error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {/* Responsive modal: mx-2 for mobile, max-w-md for desktop, always rounded */}
      <DialogContent className="p-5 sm:p-6 px-5 rounded-2xl mx-2 max-w-[95vw] sm:max-w-md">
        <DialogHeader>
          {/* Larger, bolder title with icon */}
          <DialogTitle className="text-2xl sm:text-2xl flex items-center gap-2 font-bold">
            <Sparkles className="w-7 h-7 text-indigo-500" />
            Upgrade to Pro
          </DialogTitle>
          {/* Subtext: centered, improved line height, wraps naturally */}
          <DialogDescription className="mt-2 text-gray-700 text-base leading-relaxed text-center sm:text-base">
            Unlock powerful features to supercharge your job search:
          </DialogDescription>
        </DialogHeader>
        {/* Features list with more vertical spacing */}
        <ul className="my-7 space-y-4 text-base text-gray-800">
          <li className="flex items-center gap-2">
            <Sparkles className="w-5 h-5 text-blue-500" />
            <span>Personalized AI job insights</span>
          </li>
          <li className="flex items-center gap-2">
            <Users className="w-5 h-5 text-purple-500" />
            <span>Recommended key contacts to network with</span>
          </li>
          <li className="flex items-center gap-2">
            <Bell className="w-5 h-5 text-green-500" />
            <span>Unlimited daily alerting</span>
          </li>
        </ul>
        {/* Price with more margin */}
        <div className="text-xl font-semibold text-indigo-700 mb-6">$12.99/month</div>
        <DialogFooter>
          <Button
            className="w-full h-12 text-base bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 text-white rounded-lg"
            onClick={handleUpgrade}
            disabled={isLoading}
          >
            {isLoading ? 'Redirecting...' : 'Upgrade to Pro'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 