'use client'

import { useState, useEffect } from 'react'
import { FiTrash2, FiChevronDown, FiChevronRight } from 'react-icons/fi'
import { ConfirmDialog } from './ConfirmDialog'
import { formatRoleForDisplay } from '@/lib/roles'
import { motion } from 'framer-motion'
import { Bell } from 'lucide-react'
import { CompanyLogo } from './CompanyLogo'
import { createClient } from '@/lib/supabase'

interface AlertsTableProps {
  companies: string[]
  role: string[] | null
  onDeleteCompany: (companyToDelete: string) => Promise<void>
  experience?: string | null
  location?: string | null
}

interface CompanyData {
  name: string;
  logo_url: string;
}

export const DEFAULT_LOGO = '/default-company-logo.png'

export function AlertsTable({
  companies,
  role,
  onDeleteCompany,
  experience,
  location
}: AlertsTableProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [confirmDialog, setConfirmDialog] = useState({
    isOpen: false,
    companyToDelete: ''
  })
  const [companyData, setCompanyData] = useState<Record<string, string>>({})

  useEffect(() => {
    async function fetchCompanyData() {
      if (companies.length === 0) return;
      
      const supabase = createClient()
      const { data } = await supabase
        .from('companies')
        .select('name, logo_url')
        .in('name', companies)

      if (data) {
        const logoMap = data.reduce((acc, company) => ({
          ...acc,
          [company.name]: company.logo_url
        }), {})
        setCompanyData(logoMap)
      }
    }

    fetchCompanyData()
  }, [companies])

  const handleDeleteClick = (company: string, e: React.MouseEvent) => {
    e.stopPropagation()
    setConfirmDialog({
      isOpen: true,
      companyToDelete: company
    })
  }

  return (
    <div className="space-y-4">
      <section className="bg-white rounded-xl border border-gray-200 overflow-hidden">
        <div 
          onClick={() => setIsExpanded(!isExpanded)}
          className="flex items-center justify-between px-4 sm:px-6 py-4 cursor-pointer hover:bg-gray-50 transition-colors"
        >
          <div className="flex items-center gap-4">
            {companies.length > 0 ? (
              <div className="flex -space-x-2">
                {companies.slice(0, 3).map((company) => (
                  <div 
                    key={company}
                    className="relative w-7 h-7 overflow-hidden"
                  >
                    <CompanyLogo
                      name={company}
                      logoUrl={companyData[company] || null}
                      size={28}
                    />
                  </div>
                ))}
                {companies.length > 3 && (
                  <div className="w-7 h-7 rounded-full bg-gray-50 border-2 border-white flex items-center justify-center text-xs font-medium text-gray-600">
                    +{companies.length - 3}
                  </div>
                )}
              </div>
            ) : (
              <div className="w-7 h-7 rounded-full bg-gray-50 border border-gray-200 flex items-center justify-center">
                <Bell className="w-4 h-4 text-gray-400" />
              </div>
            )}

            <div className="flex flex-col">
              <h2 className="text-xl sm:text-2xl font-semibold text-gray-900">
                My Alerts
              </h2>
              {companies.length > 0 ? (
                <p className="text-sm text-gray-600">
                  Tracking {companies.length} {companies.length === 1 ? 'company' : 'companies'}
                </p>
              ) : (
                <p className="text-sm text-gray-600">
                  Get notified about new job opportunities
                </p>
              )}
            </div>
          </div>

          {companies.length > 0 && (
            isExpanded ? 
              <FiChevronDown className="text-gray-400 text-xl flex-shrink-0" /> : 
              <FiChevronRight className="text-gray-400 text-xl flex-shrink-0" />
          )}
        </div>

        {isExpanded && companies.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
          >
            <div className="border-t border-gray-100 px-4 sm:px-6 py-4">
              <div className="space-y-2">
                {companies.map((company) => (
                  <motion.div
                    key={company}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <div className="flex items-center gap-3 min-w-0">
                      <div className="relative w-8 h-8 overflow-hidden flex-shrink-0">
                        <CompanyLogo
                          name={company}
                          logoUrl={companyData[company] || null}
                          size={32}
                        />
                      </div>
                      <div className="min-w-0">
                        <div className="font-medium text-gray-900 truncate">{company}</div>
                        <div className="text-sm text-gray-500 truncate">
                          {formatRoleForDisplay(Array.isArray(role) ? role[0] : role)}
                        </div>
                      </div>
                    </div>
                    <button
                      onClick={(e) => handleDeleteClick(company, e)}
                      className="p-2 text-gray-400 hover:text-red-500 transition-colors flex-shrink-0"
                      aria-label={`Remove ${company}`}
                    >
                      <FiTrash2 className="text-lg" />
                    </button>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        )}

        {companies.length === 0 && (
          <div className="px-4 sm:px-6 py-10 text-center border-t border-gray-100">
            <div className="max-w-sm mx-auto space-y-5">
              <div className="flex justify-center">
                <div className="w-12 h-12 rounded-full bg-[#4F46E5]/5 flex items-center justify-center">
                  <Bell className="w-6 h-6 text-[#4F46E5]" />
                </div>
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-medium text-gray-900">
                  No alerts yet
                </h3>
                <p className="text-sm text-gray-600">
                  Create your first alert in seconds and be first to apply when roles open up
                </p>
              </div>
            </div>
          </div>
        )}
      </section>

      <ConfirmDialog
        isOpen={confirmDialog.isOpen}
        onClose={() => setConfirmDialog({ isOpen: false, companyToDelete: '' })}
        onConfirm={() => {
          onDeleteCompany(confirmDialog.companyToDelete)
          setConfirmDialog({ isOpen: false, companyToDelete: '' })
        }}
        title="Delete Alert"
        message={`Are you sure you want to delete the alert for ${confirmDialog.companyToDelete}?`}
      />
    </div>
  )
}