import Image from 'next/image';
import { formatLogoUrl } from '@/lib/companies';

interface CompanyLogoProps {
  name: string;
  logoUrl: string | null;
  size?: number;
  className?: string;
}

export function CompanyLogo({ name, logoUrl, size = 32, className = '' }: CompanyLogoProps) {
  // Use database URL if available, otherwise generate one
  const imageUrl = logoUrl || formatLogoUrl(name);
  const defaultFallback = '/default-company-logo.png';

  return (
    <div 
      className={`relative ${className}`} 
      style={{ width: size, height: size }}
    >
      <Image
        src={imageUrl}
        alt={`${name} logo`}
        fill
        sizes={`${size}px`}
        className="object-contain"
        onError={(e) => {
          const img = e.target as HTMLImageElement;
          img.src = defaultFallback;
        }}
      />
    </div>
  );
}