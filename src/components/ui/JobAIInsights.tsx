import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { <PERSON>rk<PERSON>, Copy, Check, Target, Lightbulb, Star } from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button"
import { toast } from 'react-hot-toast';

interface JobAIInsightsProps {
  jobTitle: string;
  companyName: string;
  description?: string;
  yearsOfExperience?: number;
  department?: string;
}

interface InterviewProcess {
  keyFocus: string;
  topQuestionAreas: string[];
  standoutSuggestion: string;
  interviewTip: string;
}

interface Insights {
  linkedinMessage: string;
  interviewProcess: InterviewProcess;
}

export function JobAIInsights({ 
  jobTitle,
  companyName,
  description,
  yearsOfExperience,
  department
}: JobAIInsightsProps) {
  const [insights, setInsights] = useState<Insights | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    async function generateInsights() {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch('/api/job-insights', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            jobTitle,
            companyName,
            description,
            yearsOfExperience,
            department,
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to generate insights');
        }

        const data = await response.json();
        setInsights(data);
      } catch (err) {
        console.error('Error generating insights:', err);
        setError('Failed to generate insights. Please try again later.');
      } finally {
        setLoading(false);
      }
    }

    generateInsights();
  }, [jobTitle, companyName, description, yearsOfExperience, department]);

  const handleCopy = async () => {
    if (!insights?.linkedinMessage) return;
    
    try {
      await navigator.clipboard.writeText(insights.linkedinMessage);
      setCopied(true);
      toast.success('Message copied to clipboard!');
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      toast.error('Failed to copy message');
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse space-y-4">
          {[1, 2].map((i) => (
            <Card key={i}>
              <CardHeader>
                <div className="h-6 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2 mt-2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center text-red-600 p-4">
        {error}
      </div>
    );
  }

  if (!insights) {
    return null;
  }

  return (
    <div className="space-y-4">
      {/* LinkedIn Message */}
      <Card>
        <CardHeader className="relative">
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-blue-500" />
            Suggested LinkedIn Message
          </CardTitle>
          <CardDescription>
            Personalized message for reaching out to the hiring manager
          </CardDescription>
          <Button
            variant="outline"
            size="sm"
            className="absolute top-6 right-6"
            onClick={handleCopy}
          >
            {copied ? (
              <>
                <Check className="h-4 w-4 mr-2" />
                Copied!
              </>
            ) : (
              <>
                <Copy className="h-4 w-4 mr-2" />
                Copy
              </>
            )}
          </Button>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-600">
            {insights.linkedinMessage}
          </p>
        </CardContent>
      </Card>

      {/* Interview Process Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-blue-500" />
            Interview Process Insights
          </CardTitle>
          <CardDescription>
            Key areas to focus on and preparation tips
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Key Focus */}
            <div className="space-y-2">
              <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                <Target className="h-4 w-4 text-blue-500" />
                Key Focus
              </h3>
              <p className="text-sm text-gray-600 pl-6">
                {insights.interviewProcess.keyFocus}
              </p>
            </div>

            {/* Top Question Areas */}
            <div className="space-y-2">
              <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                <Lightbulb className="h-4 w-4 text-blue-500" />
                Top Question Areas
              </h3>
              <ul className="list-disc pl-10 space-y-1 text-sm text-gray-600">
                {insights.interviewProcess.topQuestionAreas.map((area, index) => (
                  <li key={index}>{area}</li>
                ))}
              </ul>
            </div>

            {/* Standout Suggestion */}
            <div className="space-y-2">
              <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                <Star className="h-4 w-4 text-blue-500" />
                How to Stand Out
              </h3>
              <p className="text-sm text-gray-600 pl-6">
                {insights.interviewProcess.standoutSuggestion}
              </p>
            </div>

            {/* Interview Tip */}
            <div className="space-y-2">
              <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                <Sparkles className="h-4 w-4 text-blue-500" />
                Pro Tip
              </h3>
              <p className="text-sm text-gray-600 pl-6">
                {insights.interviewProcess.interviewTip}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 