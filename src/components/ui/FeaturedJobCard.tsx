import React from 'react';
import { MapPin, Lock, Building, Clock, Users, ExternalLink } from 'lucide-react';
import Image from 'next/image';
import { CompanyLogo } from './CompanyLogo';

export const FeaturedJobCard = () => {
  return (
    <div className="relative bg-white rounded-2xl border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group">
      {/* Glow Effect */}
      <div className="absolute -inset-1 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-2xl blur opacity-20 group-hover:opacity-30 transition-opacity duration-300" />
      
      {/* Card Content */}
      <div className="relative bg-white rounded-2xl p-6">
        {/* Status Badge */}
        <div className="absolute top-4 right-4">
          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-50 text-red-600">
            <span className="animate-pulse mr-1">🔥</span> Just Posted
          </span>
        </div>

        {/* Company Logo and Info */}
        <div className="flex items-start gap-4">
          <div className="w-16 h-16 rounded-xl bg-gray-50 border border-gray-100 flex items-center justify-center flex-shrink-0 p-2">
            <CompanyLogo
              name="OpenAI"
              logoUrl={null}
              size={48}
              className="rounded"
            />
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="text-xl font-semibold text-gray-900 mb-1 truncate">
              Senior Product Manager
            </h3>
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-gray-600">
                <Building className="w-4 h-4" />
                <span>OpenAI</span>
              </div>
              <div className="flex items-center gap-2 text-gray-600">
                <MapPin className="w-4 h-4" />
                <span>San Francisco, CA</span>
              </div>
              <div className="flex items-center gap-2 text-gray-500">
                <Clock className="w-4 h-4" />
                <span className="text-sm">Posted 5 minutes ago</span>
              </div>
            </div>
          </div>
        </div>

        {/* Divider */}
        <div className="h-px bg-gray-100 my-6" />

        {/* Pro Feature Preview */}
        <div className="bg-gray-50 rounded-xl p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2 text-gray-900">
              <Users className="w-5 h-5 text-indigo-600" />
              <span className="font-medium">Hiring Team</span>
            </div>
            <span className="text-xs font-medium text-indigo-600 bg-indigo-50 px-2 py-1 rounded-full">
              Pro Feature
            </span>
          </div>
          <div className="flex items-center gap-3">
            <div className="flex -space-x-2">
              <div className="w-8 h-8 rounded-full bg-gray-200 border-2 border-white flex items-center justify-center">
                <span className="text-xs font-medium text-gray-600">JD</span>
              </div>
              <div className="w-8 h-8 rounded-full bg-gray-200 border-2 border-white flex items-center justify-center">
                <span className="text-xs font-medium text-gray-600">SK</span>
              </div>
              <div className="w-8 h-8 rounded-full bg-gray-200 border-2 border-white flex items-center justify-center">
                <span className="text-xs font-medium text-gray-600">+2</span>
              </div>
            </div>
            <span className="text-sm text-gray-600">4 key contacts available</span>
          </div>
        </div>

        {/* CTA Button */}
        <button className="mt-6 w-full bg-gray-100 text-gray-600 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 transition-colors flex items-center justify-center gap-2">
          View Key Contacts
          <Lock className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
}; 