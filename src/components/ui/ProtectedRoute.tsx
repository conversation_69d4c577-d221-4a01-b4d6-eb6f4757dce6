'use client'

import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'

export function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const { user, loading, session } = useAuth()
  const router = useRouter()

  useEffect(() => {
    const verifySession = async () => {
      if (!loading) {
        if (!user || !session) {
          router.push('/signin')
        }
      }
    }

    verifySession()
  }, [user, loading, session, router])

  if (loading) {
    return <div>Loading...</div>
  }

  return user && session ? <>{children}</> : null
}
