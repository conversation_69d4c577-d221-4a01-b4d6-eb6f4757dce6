'use client'

import { useState } from 'react'
import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  RiDashboardLine, 
  RiNotificationLine, 
  RiSettings4Line,
  RiMenuLine,
  RiCloseLine
} from 'react-icons/ri'
import { Search, Sparkles } from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { useUserProfile } from '@/contexts/UserProfileContext'
import { SubscribeButton } from '@/components/SubscribeButton'

// Navigation items configuration
const navItems = [
  {
    label: 'Dashboard',
    icon: RiDashboardLine,
    href: '/dashboard'
  },
  {
    label: 'Job Hunt',
    icon: Search,
    href: '/dashboard/job-hunt'
  },
  {
    label: 'My Alerts',
    icon: RiNotificationLine,
    href: '/dashboard/alerts'
  },
  {
    label: 'Settings',
    icon: RiSettings4Line,
    href: '/dashboard/settings'
  }
]

interface DashboardLayoutProps {
  children: React.ReactNode
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const pathname = usePathname()
  const { user } = useAuth()
  const { isProUser } = useUserProfile()

  // Navigation item component
  const NavItem = ({ item }: { item: typeof navItems[0] }) => {
    const isActive = pathname === item.href
    const Icon = item.icon

    return (
      <Link
        href={item.href}
        className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors duration-200
          ${isActive 
            ? 'bg-[#4F46E5]/10 text-[#4F46E5] font-semibold' 
            : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
          }`}
      >
        <Icon className={`text-xl ${isActive ? 'text-[#4F46E5]' : 'text-gray-500'}`} />
        <span className="text-sm">{item.label}</span>
      </Link>
    )
  }

  const SidebarContent = () => {
    const { user } = useAuth();
    
    return (
      <>
        <div className="mb-8">
          <h1 className="text-xl font-semibold text-gray-900">Awaloon</h1>
          <p className="text-sm text-gray-500 mt-1">Job Search Dashboard</p>
        </div>
        <nav className="space-y-1">
          {navItems.map((item) => (
            <NavItem key={item.href} item={item} />
          ))}
        </nav>
        
        {/* Upgrade button - only show for non-pro users */}
        {!isProUser && user?.id && (
          <div className="mt-8 pt-8 border-t border-gray-200">
            <SubscribeButton
              userId={user.id}
              className="w-full inline-flex items-center justify-center px-4 py-2 bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 text-white rounded-lg transition-colors gap-2"
            >
              <Sparkles className="w-4 h-4" />
              <span>Upgrade to Pro</span>
            </SubscribeButton>
          </div>
        )}
      </>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Desktop Sidebar */}
      <aside className="hidden md:flex flex-col w-64 bg-white border-r border-gray-200 p-6">
        <SidebarContent />
      </aside>

      {/* Mobile Menu Button */}
      <button
        onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        className="md:hidden fixed top-4 left-4 z-50 p-2 rounded-lg bg-white shadow-lg text-gray-600 hover:text-gray-900"
        aria-label="Toggle menu"
      >
        {isMobileMenuOpen ? (
          <RiCloseLine className="text-2xl" />
        ) : (
          <RiMenuLine className="text-2xl" />
        )}
      </button>

      {/* Mobile Sidebar */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 0.5 }}
              exit={{ opacity: 0 }}
              onClick={() => setIsMobileMenuOpen(false)}
              className="md:hidden fixed inset-0 bg-black z-40"
            />
            <motion.aside
              initial={{ x: '-100%' }}
              animate={{ x: 0 }}
              exit={{ x: '-100%' }}
              transition={{ type: 'tween', duration: 0.2 }}
              className="fixed inset-y-0 left-0 w-64 bg-white z-50 p-6 md:hidden shadow-xl"
            >
              <SidebarContent />
            </motion.aside>
          </>
        )}
      </AnimatePresence>

      {/* Main Content */}
      <main className="flex-1 overflow-auto">
        <div className="p-4 sm:p-8">
          {children}
        </div>
      </main>
    </div>
  )
} 
