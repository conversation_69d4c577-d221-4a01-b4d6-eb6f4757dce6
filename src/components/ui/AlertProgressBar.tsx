'use client'

import { useEffect, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import <PERSON>fetti from 'react-confetti'

interface AlertProgressBarProps {
  alertCount: number
  onQuickAddClick: () => void
}

export function AlertProgressBar({ alertCount, onQuickAddClick }: AlertProgressBarProps) {
  const [showConfetti, setShowConfetti] = useState(false)
  const [shouldShow, setShouldShow] = useState(alertCount < 5)
  const targetGoal = 5
  const progress = Math.min((alertCount / targetGoal) * 100, 100)
  const isComplete = alertCount >= targetGoal

  useEffect(() => {
    // Show the progress bar when user has less than 5 alerts
    if (alertCount < targetGoal) {
      setShouldShow(true)
      return
    }

    // Show celebration when user reaches or exceeds target goal
    if (alertCount >= targetGoal) {
      setShowConfetti(true)
      
      // Hide the entire component after celebration
      const timer = setTimeout(() => {
        setShow<PERSON>on<PERSON>tti(false)
        setShouldShow(false)
      }, 4500)

      return () => clearTimeout(timer)
    }
  }, [alertCount, targetGoal]) // Only depend on external props that affect the logic

  console.log('AlertProgressBar render - Current state:', {
    shouldShow,
    showConfetti,
    alertCount,
    isComplete
  })

  // Don't render if we should hide the component
  if (!shouldShow) {
    console.log('Component hidden - shouldShow is false')
    return null
  }

  return (
    <div className="bg-white rounded-xl border border-gray-200 shadow-[0_4px_6px_rgba(0,0,0,0.05)] p-3 sm:p-5 mb-6">
      {showConfetti && (
        <div className="fixed inset-0 pointer-events-none z-50">
          <Confetti
            width={window.innerWidth}
            height={window.innerHeight}
            recycle={false}
            numberOfPieces={500}
          />
        </div>
      )}

      <div className="space-y-4">
        <AnimatePresence mode="wait">
          <motion.h3
            key={isComplete ? 'complete' : 'progress'}
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            className="text-[18px] font-medium text-gray-700 text-center tracking-normal"
          >
            {isComplete ? (
              <span>{"🎉 You're tracking 5 companies — stay tuned for more jobs!"}</span>
            ) : (
              <span>
                {"You're just a few alerts away from being the first to apply"} <span className="ml-1">({alertCount}/{targetGoal}) 🚀</span>
              </span>
            )}
          </motion.h3>
        </AnimatePresence>

        <div className="flex justify-center">
          <div className="w-[95%]">
            <div 
              className={`relative h-[8px] bg-gray-100 rounded-full overflow-hidden transition-all group
                ${!isComplete && 'cursor-pointer hover:ring-2 hover:ring-offset-2 hover:ring-purple-200'}`}
              onClick={() => !isComplete && onQuickAddClick()}
            >
              <motion.div
                className={`absolute left-0 top-0 h-full rounded-full transition-colors duration-200
                  ${isComplete 
                    ? 'bg-gradient-to-r from-green-400 to-green-500' 
                    : 'bg-gradient-to-r from-[#A78BFA] to-[#7C3AED] group-hover:from-[#9B78E8] group-hover:to-[#6E35D6]'}`}
                initial={{ width: 0 }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 0.5, ease: 'easeOut' }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 