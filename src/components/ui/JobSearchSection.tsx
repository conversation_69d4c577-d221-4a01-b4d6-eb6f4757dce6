'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import { CompanySearch } from './CompanySearch'
import { JobFilters } from './JobFilters'
import { useFilter } from '@/contexts/FilterContext'
import { createClient } from '@/lib/supabase'
import { useAuth } from '@/contexts/AuthContext'
import { MapPin, Bell, Search, Sparkles, X } from 'lucide-react'
import Image from "next/image"
import Link from 'next/link'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import toast from 'react-hot-toast'
import { formatDistanceToNow } from 'date-fns'
import { JobRole, roleDisplayNames, formatRoleForDisplay } from '@/lib/roles'
import { Input } from "@/components/ui/input"
import { sendSlackNotification } from '@/lib/slack'
import { motion } from 'framer-motion'
import { formatLogoUrl } from '@/lib/companies'
import { CompanyLogo } from './CompanyLogo'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import React from 'react'

// Constants
const DEFAULT_LOGO = '/default-company-logo.png'

// Debounce utility function
function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): T & { cancel: () => void } {
  let timeoutId: NodeJS.Timeout | null = null;
  
  const debounced = ((...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    
    timeoutId = setTimeout(() => {
      func(...args);
    }, delay);
  }) as T & { cancel: () => void };
  
  debounced.cancel = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
  };
  
  return debounced;
}

// Update the popularAlerts data
const popularAlerts = [
  {
    company: 'OpenAI',
    role: 'Software Engineer',
    roleFilter: JobRole.SOFTWARE_ENGINEER
  },
  {
    company: 'Figma',
    role: 'Product Designer',
    roleFilter: JobRole.PRODUCT_DESIGNER
  },
  {
    company: 'Anthropic',
    role: 'Product Manager',
    roleFilter: JobRole.PRODUCT_MANAGER
  }
];

// Update AlertCard component
function AlertCard({ 
  company, 
  role, 
  onSelect
}: { 
  company: string; 
  role: string; 
  onSelect: () => void;
}) {
  const [logoUrl, setLogoUrl] = useState<string | null>(null);

  useEffect(() => {
    async function fetchCompanyLogo() {
      const supabase = createClient()
      const { data } = await supabase
        .from('companies')
        .select('logo_url')
        .eq('name', company)
        .single()

      if (data) {
        setLogoUrl(data.logo_url)
      }
    }

    fetchCompanyLogo()
  }, [company]);

  return (
    <button 
      onClick={onSelect}
      className="group flex items-center gap-4 p-3 rounded-lg border border-gray-200 hover:border-gray-300 bg-white transition-all hover:shadow-sm w-full"
    >
      <div className="relative w-12 h-12">
        <CompanyLogo
          name={company}
          logoUrl={logoUrl}
          size={48}
        />
      </div>
      
      <div className="flex-1 text-left">
        <h3 className="font-medium text-gray-900 group-hover:text-[#4F46E5] transition-colors">
          {company}
        </h3>
        <p className="text-sm text-gray-500">{role}</p>
      </div>

      <Bell className="w-4 h-4 text-gray-400 group-hover:text-[#4F46E5] transition-colors" />
    </button>
  );
}

// Add after the existing interfaces
interface JobListing {
  id: number;
  title: string;
  company_name: string;
  category_dept: string;
  date_posted: string;
  years_of_experience: number | null;
  location: string;
  url: string;
  companies: {
    id: number;
    name: string;
    logo_url: string;
    careers_page_url?: string;
  } | null;
}

interface UserProfile {
  id: string
  filter_companies: string[]
  filter_role: string | null
  filter_experience: string | null
  filter_location: string | null
  filter_alert_frequency: string | null
}

// Add this helper function near the top of the file, after the interfaces
const formatLocations = (location: string | null | undefined): string => {
  if (!location || location.trim() === '') return 'Not available';
  
  try {
    // Check if it's a JSON string array
    const locations = JSON.parse(location);
    if (Array.isArray(locations)) {
      // Filter out any empty strings and join with bullets
      const validLocations = locations.filter(loc => loc && loc.trim() !== '');
      return validLocations.length > 0 ? validLocations.join(' • ') : 'Not available';
    }
    // If it's a single location string
    return location.trim() || 'Not available';
  } catch {
    // If it's not JSON, return the original string if it's not empty
    return location.trim() || 'Not available';
  }
};

// Add to existing interfaces
interface CompanyRequestForm {
  companyName: string;
  email?: string;  // Optional for anonymous users
}

// Add this new component for mobile job cards
function MobileJobCard({ job }: { job: JobListing }) {
  return (
    <Link
      href={job.url || '#'}
      target="_blank"
      rel="noopener noreferrer"
      className="block hover:bg-gray-50 transition-colors p-5"
    >
      <div className="flex items-start gap-4">
        <div className="relative flex-shrink-0 w-14 h-14">
          <CompanyLogo
            name={job.company_name}
            logoUrl={job.companies?.logo_url || null}
            size={56}
          />
        </div>

        <div className="flex-1 min-w-0 space-y-2">
          <h3 className="text-lg font-semibold text-gray-900 leading-tight">
            {job.title}
          </h3>

          <div className="flex items-center gap-2 flex-wrap">
            <span className="text-base font-medium text-gray-700">
              {job.company_name}
            </span>
            <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
              {formatRoleForDisplay(job.category_dept)}
            </span>
          </div>

          <div className="flex items-center gap-1.5 text-sm text-gray-600 mb-1">
            <MapPin className="h-3.5 w-3.5 text-gray-400 flex-shrink-0" />
            <span>{formatLocations(job.location)}</span>
          </div>

          <div className="text-sm text-gray-500">
            {job.date_posted 
              ? formatDistanceToNow(new Date(job.date_posted), { addSuffix: true })
              : 'N/A'}
          </div>
        </div>
      </div>
    </Link>
  );
}

// Update AlertPreview component
function AlertPreview({ 
  companies, 
  role,
  onRemoveCompany,
  isAuthenticated,
  onSignupClick,
  onCreateAlert
}: { 
  companies: string[];
  role: string[];
  onRemoveCompany: (company: string) => void;
  isAuthenticated: boolean;
  onSignupClick?: () => void;
  onCreateAlert?: () => void;
}) {
  if (companies.length === 0) return null;

  // Helper function to format role for display
  const getDisplayRole = (roleValue: string) => {
    if (roleValue === '*') return 'All Roles';
    // If it's an enum value, convert to display name
    if (roleValue in JobRole) {
      return roleDisplayNames[roleValue as JobRole];
    }
    // If it's already a display name, use it as is
    return roleValue;
  };

  return (
    <div className="mt-4 space-y-3">
      {/* Alert Description */}
      <div className="flex items-center gap-3 flex-wrap">
        <span className="text-gray-600">Track</span>
        <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-[#4F46E5]/10 text-[#4F46E5]">
          {getDisplayRole(role[0])}
        </span>
        <span className="text-gray-600">roles at</span>
        <div className="flex flex-wrap items-center gap-2">
          {companies.map((company) => (
            <div 
              key={company}
              className="inline-flex items-center gap-1.5 px-3 py-1 bg-gray-50 border border-gray-200 rounded-full text-sm"
            >
              <span className="text-gray-700">{company}</span>
              <button
                onClick={() => onRemoveCompany(company)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-3.5 h-3.5" />
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Action Button */}
      <button
        onClick={isAuthenticated ? onCreateAlert : onSignupClick}
        className="w-full px-4 py-2.5 bg-[#4F46E5] hover:bg-[#4338CA] text-white font-medium rounded-lg transition-colors flex items-center justify-center gap-2"
      >
        <Bell className="w-4 h-4" />
        {isAuthenticated ? 'Create Alert' : 'Sign up to get notified'}
      </button>
    </div>
  );
}

// Add after JobListing interface
type JobListingResponse = {
  id: number;
  title: string;
  company_name: string;
  category_dept: string;
  date_posted: string;
  years_of_experience: number | null;
  location: string;
  url: string;
  companies: {
    id: number;
    name: string;
    logo_url: string;
    careers_page_url?: string;
  };
}

// Add after popularAlerts
export interface JobSearchSectionProps {
  isAuthenticated: boolean;
  initialCompanies?: string[];
  initialRole?: string[];
  onRoleChange: (roles: string[]) => void;
  jobs?: JobListing[];
  compact?: boolean;
}

// Helper function to format role for display
const getDisplayRole = (roleValue: string) => {
  if (roleValue === '*') return 'All Roles';
  // If it's an enum value, convert to display name
  if (roleValue in JobRole) {
    return roleDisplayNames[roleValue as JobRole];
  }
  // If it's already a display name, use it as is
  return roleValue;
};

export function JobSearchSection({
  isAuthenticated,
  initialCompanies = [],
  initialRole = ['*'],
  onRoleChange,
  jobs = [],
  compact = false
}: JobSearchSectionProps) {
  // Local state for search functionality
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<string[]>([]);
  const [fetchedJobs, setFetchedJobs] = useState<JobListing[]>([]);
  const [loadingJobs, setLoadingJobs] = useState(false);

  // For compact mode, use FilterContext; for full mode, use local state
  const { selectedCompanies: contextCompanies, setSelectedCompanies: setContextCompanies } = useFilter();
  const [localSelectedCompanies, setLocalSelectedCompanies] = useState<string[]>([]);

  // Determine which state to use based on mode
  const selectedCompanies = compact ? contextCompanies : localSelectedCompanies;
  const setSelectedCompanies = compact ? setContextCompanies : setLocalSelectedCompanies;

  // Memoize the current role to prevent unnecessary re-renders
  const currentRole = useMemo(() => initialRole[0] || '*', [initialRole]);

  // Debounced job fetching function
  const debouncedFetchJobs = useCallback(
    debounce(async (companies: string[], role: string) => {
      if (compact || companies.length === 0) {
        setFetchedJobs([]);
        return;
      }

      setLoadingJobs(true);
      try {
        const supabase = createClient();
        
        // Get jobs from the last 2 weeks for selected companies
        const twoWeeksAgo = new Date();
        twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 14);
        
        let query = supabase
          .from('job_listings')
          .select(`
            id,
            title,
            company_name,
            category_dept,
            date_posted,
            years_of_experience,
            location,
            url,
            companies (
              id,
              name,
              logo_url,
              careers_page_url
            )
          `)
          .in('company_name', companies)
          .gte('date_posted', twoWeeksAgo.toISOString())
          .order('date_posted', { ascending: false })
          .limit(10);

        // Filter by role if not "all roles"
        if (role !== '*') {
          query = query.eq('category_dept', role);
        }

        const { data, error } = await query;

        if (error) {
          console.error('Error fetching jobs:', error);
          setFetchedJobs([]);
        } else {
          // Map the data to match our JobListing interface
          const mappedJobs: JobListing[] = (data || []).map(job => ({
            id: job.id,
            title: job.title,
            company_name: job.company_name,
            category_dept: job.category_dept,
            date_posted: job.date_posted,
            years_of_experience: job.years_of_experience,
            location: job.location,
            url: job.url,
            companies: job.companies && job.companies.length > 0 ? job.companies[0] : null
          }));
          setFetchedJobs(mappedJobs);
        }
      } catch (error) {
        console.error('Error fetching jobs:', error);
        setFetchedJobs([]);
      } finally {
        setLoadingJobs(false);
      }
    }, 500), // 500ms debounce
    [compact]
  );

  // Initialize companies from props only for full mode
  useEffect(() => {
    if (!compact && initialCompanies.length > 0 && localSelectedCompanies.length === 0) {
      setLocalSelectedCompanies(initialCompanies);
    }
  }, [compact, initialCompanies, localSelectedCompanies.length]);

  // Optimized useEffect with stable dependencies
  useEffect(() => {
    // Create stable string representations for comparison
    const companiesKey = selectedCompanies.sort().join(',');
    const roleKey = currentRole;
    
    if (companiesKey && roleKey) {
      debouncedFetchJobs(selectedCompanies, currentRole);
    }

    // Cleanup function to cancel pending debounced calls
    return () => {
      debouncedFetchJobs.cancel?.();
    };
  }, [selectedCompanies.join(','), currentRole, debouncedFetchJobs]); // Stable dependencies

  // Handle search input changes - matches CompanySearch interface
  const handleSearchInputChange = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    
    // Search real company data
    if (value.trim()) {
      try {
        const supabase = createClient();
        const { data, error } = await supabase
          .from('companies')
          .select('name')
          .ilike('name', `%${value}%`)
          .limit(8)
          .order('name');

        if (error) {
          console.error('Error searching companies:', error);
          setSearchResults([]);
        } else {
          setSearchResults(data?.map(company => company.name) || []);
        }
      } catch (error) {
        console.error('Error searching companies:', error);
        setSearchResults([]);
      }
    } else {
      setSearchResults([]);
    }
  }, []);

  // Handle company selection - unified behavior
  const handleCompanySelect = useCallback((company: string) => {
    setSelectedCompanies(prev => {
      if (Array.isArray(prev) && !prev.includes(company)) {
        return [...prev, company];
      }
      return prev;
    });
    // Clear search after selection for clean UX
    setSearchTerm('');
    setSearchResults([]);
  }, [setSelectedCompanies]);

  // Handle company removal
  const handleCompanyRemove = useCallback((company: string) => {
    setSelectedCompanies(prev => {
      if (Array.isArray(prev)) {
        return prev.filter(c => c !== company);
      }
      return prev;
    });
  }, [setSelectedCompanies]);

  // Handle company request (placeholder)
  const handleCompanyRequest = useCallback(async (company: string, email?: string) => {
    console.log('Company request:', { company, email });
  }, []);

  // Compact mode - clean search field that adds to parent context
  if (compact) {
    return (
      <div className="w-full">
        <CompanySearch
          searchTerm={searchTerm}
          selectedCompanies={[]} // Always empty - don't show pills in search
          searchResults={searchResults}
          onSearchChange={handleSearchInputChange}
          onCompanySelect={handleCompanySelect}
          onCompanyRemove={() => {}} // Not used in compact mode
          onCompanyRequest={handleCompanyRequest}
          isAuthenticated={isAuthenticated}
        />
        {/* No AlertPreview in compact mode - parent handles unified confirmation */}
      </div>
    );
  }

  // Full mode - complete layout with local state
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 space-y-6">
      {/* Role Selection */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-700">
          What role are you looking for?
        </label>
        <Select
          value={initialRole[0]}
          onValueChange={(value) => onRoleChange([value])}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select a role">
              {initialRole[0] === '*' ? 'All Roles' : roleDisplayNames[initialRole[0] as JobRole]}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="*">All Roles</SelectItem>
            {Object.entries(JobRole).map(([key, value]) => (
              <SelectItem key={key} value={key}>
                {roleDisplayNames[value]}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Company Search */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-700">
          Which companies interest you?
        </label>
        <CompanySearch
          searchTerm={searchTerm}
          selectedCompanies={selectedCompanies}
          searchResults={searchResults}
          onSearchChange={handleSearchInputChange}
          onCompanySelect={handleCompanySelect}
          onCompanyRemove={handleCompanyRemove}
          onCompanyRequest={handleCompanyRequest}
          isAuthenticated={isAuthenticated}
        />
      </div>

      {/* Alert Preview - only in full mode */}
      <AlertPreview
        companies={selectedCompanies}
        role={initialRole}
        onRemoveCompany={handleCompanyRemove}
        isAuthenticated={isAuthenticated}
      />

      {/* Job Results */}
      {selectedCompanies.length > 0 && (
        <div className="border-t pt-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Recent Jobs {selectedCompanies.length > 0 && `at ${selectedCompanies.join(', ')}`}
          </h3>
          
          {loadingJobs ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500"></div>
              <span className="ml-3 text-gray-600">Finding recent jobs...</span>
            </div>
          ) : fetchedJobs.length > 0 ? (
            <div className="space-y-3">
              {fetchedJobs.slice(0, 5).map((job, index) => (
                <div key={job.id || index} className="p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors">
                  <div className="flex items-start gap-3">
                    <div className="w-10 h-10 rounded-lg overflow-hidden flex-shrink-0 bg-gray-50">
                      <CompanyLogo
                        name={job.company_name}
                        logoUrl={job.companies?.logo_url || null}
                        size={40}
                      />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 mb-1">{job.title}</h4>
                      <p className="text-sm text-gray-600 mb-2">{job.company_name}</p>
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <span>{formatLocations(job.location)}</span>
                        {job.date_posted && (
                          <span>Posted {formatDistanceToNow(new Date(job.date_posted), { addSuffix: true })}</span>
                        )}
                      </div>
                    </div>
                    {job.url && (
                      <Link
                        href={job.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="px-3 py-1.5 text-sm bg-indigo-50 text-indigo-600 rounded-md hover:bg-indigo-100 transition-colors"
                      >
                        View Job
                      </Link>
                    )}
                  </div>
                </div>
              ))}
              {fetchedJobs.length > 5 && (
                <p className="text-sm text-gray-500 text-center pt-2">
                  Showing 5 of {fetchedJobs.length} recent jobs from the last 2 weeks
                </p>
              )}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <p className="mb-2">No recent jobs found in the last 2 weeks</p>
              <p className="text-sm">Try selecting different companies or check back later</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}