'use client'

import { FiSearch, FiX, FiPlus } from 'react-icons/fi'
import { useState, useEffect, useRef } from 'react'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"

interface CompanySearchProps {
  searchTerm: string
  selectedCompanies: string[]
  searchResults: string[]
  hotCompanies?: string[]
  onSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  onCompanySelect: (company: string) => void
  onCompanyRemove: (company: string) => void
  onCompanyRequest: (company: string, email?: string) => Promise<void>
  requestedCompanies?: Set<string>
  isAuthenticated?: boolean
  className?: string
}

// Add suggested companies that will show on focus
const SUGGESTED_COMPANIES = [
  'OpenAI',
  'Anthropic',
  'Ramp',
  'Notion',
  'Perplexity',
];

export function CompanySearch({
  searchTerm,
  selectedCompanies,
  searchResults,
  hotCompanies,
  onSearchChange,
  onCompanySelect,
  onCompanyRemove,
  onCompanyRequest,
  requestedCompanies,
  isAuthenticated = false,
  className = ''
}: CompanySearchProps) {
  const [isRequestModalOpen, setIsRequestModalOpen] = useState(false)
  const [companyToRequest, setCompanyToRequest] = useState('')
  const [requestEmail, setRequestEmail] = useState('')
  const [isSearching, setIsSearching] = useState(false)
  const [isFocused, setIsFocused] = useState(false)
  const searchContainerRef = useRef<HTMLDivElement>(null)
  
  // Add debounce for search state
  useEffect(() => {
    if (searchTerm) {
      setIsSearching(true)
      const timer = setTimeout(() => {
        setIsSearching(false)
      }, 500)
      return () => clearTimeout(timer)
    }
  }, [searchTerm])

  // Add click/touch outside handler
  useEffect(() => {
    function handleOutsideEvent(event: MouseEvent | TouchEvent) {
      if (searchContainerRef.current && !searchContainerRef.current.contains(event.target as Node)) {
        setIsFocused(false)
      }
    }

    // Handle both mouse and touch events
    document.addEventListener('mousedown', handleOutsideEvent)
    document.addEventListener('touchstart', handleOutsideEvent)
    
    return () => {
      document.removeEventListener('mousedown', handleOutsideEvent)
      document.removeEventListener('touchstart', handleOutsideEvent)
    }
  }, [])

  const handleRequestClick = (company: string) => {
    setCompanyToRequest(company)
    setIsRequestModalOpen(true)
  }

  const handleRequestSubmit = async () => {
    await onCompanyRequest?.(companyToRequest, requestEmail)
    setIsRequestModalOpen(false)
    setRequestEmail('')
    setCompanyToRequest('')
  }

  const handleSuggestionClick = (company: string) => {
    onCompanySelect(company)
    setIsFocused(false)
  }

  return (
    <div className="space-y-4">
      {/* Search Input Container */}
      <div className="relative" ref={searchContainerRef}>
        <div className="relative flex items-center">
          <FiSearch className="absolute left-3 sm:left-4 text-gray-400 pointer-events-none h-5 w-5" />
          <input
            type="text"
            placeholder='Try "OpenAI" or "Anthropic"...'
            value={searchTerm}
            onChange={onSearchChange}
            onFocus={() => setIsFocused(true)}
            className={`
              w-full 
              pl-10
              sm:pl-11 
              pr-4 
              py-2
              bg-white 
              border-2 
              border-gray-200 
              rounded-lg 
              focus:outline-none 
              focus:ring-2 
              focus:ring-[#4F46E5] 
              focus:border-transparent 
              text-base
              h-12
              ${className}
            `}
            style={{
              fontSize: '16px', // Prevents zoom on mobile
              WebkitAppearance: 'none' // Removes default iOS styling
            }}
          />
          {searchTerm && (
            <button
              onClick={() => onSearchChange({ target: { value: '' } } as React.ChangeEvent<HTMLInputElement>)}
              className="absolute right-3 sm:right-4 text-gray-400 hover:text-gray-600 p-1"
            >
              <FiX className="h-5 w-5" />
            </button>
          )}
        </div>
        
        {/* Search Results Dropdown */}
        {(searchTerm || isFocused) && (
          <div 
            className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden max-h-[300px] overflow-y-auto"
            onMouseDown={(e) => e.preventDefault()} // Prevent input blur on mouse interaction
            onTouchStart={(e) => e.preventDefault()} // Prevent input blur on touch interaction
          >
            {searchTerm ? (
              // Show search results if there's a search term
              searchResults.length > 0 ? (
                <div>
                  {searchResults.map((company) => (
                    <button
                      key={company}
                      onClick={() => {
                        onCompanySelect(company)
                        onSearchChange({ target: { value: '' } } as React.ChangeEvent<HTMLInputElement>)
                        setIsFocused(false)
                      }}
                      className="w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors flex items-center gap-2"
                    >
                      <FiSearch className="h-4 w-4 text-gray-400 flex-shrink-0" />
                      <span className="truncate">{company}</span>
                    </button>
                  ))}
                </div>
              ) : requestedCompanies?.has(searchTerm) ? (
                <div className="p-4">
                  <p className="text-sm text-gray-600">
                    {"Thanks! We'll notify you when we start tracking"} {searchTerm}.
                  </p>
                </div>
              ) : (
                <div className="p-4">
                  <p className="text-sm text-gray-600 mb-2">
                    {"We're not tracking"} {searchTerm} {"yet"}
                  </p>
                  <button
                    onClick={() => handleRequestClick(searchTerm)}
                    className="w-full px-3 py-2.5 text-sm text-left bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors flex items-center gap-2"
                  >
                    <FiPlus className="h-4 w-4 flex-shrink-0" />
                    <span className="truncate">{"Request to track"} {searchTerm}</span>
                  </button>
                </div>
              )
            ) : (
              // Show suggested companies when focused but no search term
              <div className="py-2">
                <div className="px-4 py-1.5 text-xs font-medium text-gray-500 uppercase">
                  Suggested Companies
                </div>
                {SUGGESTED_COMPANIES.map((company) => (
                  <button
                    key={company}
                    onClick={() => handleSuggestionClick(company)}
                    className="w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors flex items-center gap-2"
                  >
                    <FiSearch className="h-4 w-4 text-gray-400 flex-shrink-0" />
                    <span className="truncate">{company}</span>
                  </button>
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Popular Companies - Only show when no search term */}
      {!searchTerm && hotCompanies && (
        <div className="flex overflow-x-auto scrollbar-hide gap-2 -mx-4 px-4 sm:mx-0 sm:px-0 sm:flex-wrap">
          {hotCompanies.map((company) => (
            <button
              key={company}
              onClick={() => onCompanySelect(company)}
              className="flex-none px-4 py-2.5 text-sm sm:text-base bg-gray-50 hover:bg-gray-100 
                        rounded-lg transition-colors whitespace-nowrap border border-gray-200"
            >
              {company}
            </button>
          ))}
        </div>
      )}

      {/* Request Modal */}
      <Dialog open={isRequestModalOpen} onOpenChange={setIsRequestModalOpen}>
        <DialogContent className="sm:max-w-md p-4 sm:p-6">
          <DialogHeader>
            <DialogTitle className="text-lg sm:text-xl">Request to track {companyToRequest}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <p className="text-sm text-gray-600">
              {"We'll notify you when we start tracking job listings from this company."}
            </p>
            {!isAuthenticated && (
              <Input
                type="email"
                placeholder="Email (optional)"
                value={requestEmail}
                onChange={(e) => setRequestEmail(e.target.value)}
                className="h-12"
              />
            )}
            <Button
              onClick={handleRequestSubmit}
              className="w-full h-12 text-base"
            >
              Submit Request
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}