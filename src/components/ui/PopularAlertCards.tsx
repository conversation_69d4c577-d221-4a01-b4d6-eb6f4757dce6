'use client';

import * as React from "react"
import { Card, CardContent } from "@/components/ui/card"
import Image from "next/image"
import { formatLogoUrl } from "@/lib/companies"
import { Sparkles, Bell, X, ChevronDown, ChevronLeft, ChevronRight, Check } from "lucide-react"
import { JobRole, roleDisplayNames } from '@/lib/roles'
import toast from 'react-hot-toast'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { CompanyLogo } from './CompanyLogo'
import { createClient } from '@/lib/supabase'

interface CompanyBundle {
  id?: string;
  title: string;
  description?: string;
  category?: string;
  companies: string[];
}

export { type CompanyBundle };

// Fallback bundles in case JSON loading fails
const FALLBACK_BUNDLES: CompanyBundle[] = [
  {
    id: "ai-companies",
    title: "AI Companies",
    description: "Leading AI/ML companies at the forefront of artificial intelligence",
    category: "ai",
    companies: ["OpenAI", "Anthropic", "Google DeepMind", "xAI", "Mistral AI", "Scale AI", "Cohere"]
  },
  {
    id: "saas-companies",
    title: "SaaS Companies", 
    description: "Best-in-class SaaS companies revolutionizing business software",
    category: "saas",
    companies: ["Notion", "Linear", "Ramp", "Retool", "Figma", "Webflow", "Perplexity"]
  },
  {
    id: "remote-companies",
    title: "Remote Companies",
    description: "Leading companies with excellent remote work culture",
    category: "remote",
    companies: ["GitLab", "Buffer", "Zapier", "Automattic", "InVision", "Basecamp", "Doist"]
  }
];

// Utility function to load bundles from JSON file
async function loadBundlesFromJSON(): Promise<CompanyBundle[]> {
  try {
    const response = await fetch('/data/bundles.json');
    if (!response.ok) {
      throw new Error(`Failed to load bundles: ${response.status}`);
    }
    const data = await response.json();
    return data.bundles || FALLBACK_BUNDLES;
  } catch (error) {
    console.warn('Failed to load bundles from JSON, using fallback:', error);
    return FALLBACK_BUNDLES;
  }
}

// Hook to manage bundle loading
function useBundles() {
  const [bundles, setBundles] = React.useState<CompanyBundle[]>(FALLBACK_BUNDLES);
  const [loading, setLoading] = React.useState(false); // Start with false since we have fallback
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    // Only show loading if we don't have any bundles yet
    if (bundles.length === 0) {
      setLoading(true);
    }

    loadBundlesFromJSON()
      .then(loadedBundles => {
        setBundles(loadedBundles);
        setError(null);
      })
      .catch(err => {
        setError(err.message);
        setBundles(FALLBACK_BUNDLES); // Ensure we always have fallback
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  return { bundles, loading, error };
}

// Keep the original export for backward compatibility
export const COMPANY_BUNDLES: CompanyBundle[] = FALLBACK_BUNDLES;

interface PopularAlertCardsProps {
  onSelectBundle: (companies: string[], role: string[], bundleTitle?: string) => void;
  currentRole: string[];
  addedBundles?: string[];
}

export function PopularAlertCards({ onSelectBundle, currentRole, addedBundles = [] }: PopularAlertCardsProps) {
  const [currentIndex, setCurrentIndex] = React.useState(0);
  const [selectedBundles, setSelectedBundles] = React.useState<Set<string>>(new Set());
  const [touchStartX, setTouchStartX] = React.useState<number | null>(null);
  const [touchEndX, setTouchEndX] = React.useState<number | null>(null);
  const { bundles, loading, error } = useBundles();

  // Filter out bundles that have already been added
  const availableBundles = bundles.filter(bundle => !addedBundles.includes(bundle.title));

  // If loading, show a simple loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center p-6">
        <div className="text-gray-500 text-sm">Loading...</div>
      </div>
    );
  }

  // If no bundles are available, don't render anything
  if (availableBundles.length === 0) {
    return null;
  }

  // Show error if there was an issue loading (but still show fallback bundles)
  if (error) {
    console.warn('Bundle loading error:', error);
  }

  const handleNext = () => {
    setCurrentIndex((prev) => 
      prev === availableBundles.length - 1 ? 0 : prev + 1
    );
  };

  const handlePrev = () => {
    setCurrentIndex((prev) => 
      prev === 0 ? availableBundles.length - 1 : prev - 1
    );
  };

  // Handle touch events for swipe gestures
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEndX(null);
    setTouchStartX(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEndX(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStartX || !touchEndX) return;
    
    const distance = touchStartX - touchEndX;
    const minSwipeDistance = 50;

    if (distance > minSwipeDistance) {
      // Swipe left - go to next
      handleNext();
    } else if (distance < -minSwipeDistance) {
      // Swipe right - go to previous
      handlePrev();
    }
  };

  const handleBundleToggle = (bundle: CompanyBundle) => {
    const bundleId = bundle.id || bundle.title;
    const isSelected = selectedBundles.has(bundleId);
    
    if (isSelected) {
      // Remove bundle - pass the bundle companies so parent can remove them specifically
      setSelectedBundles(prev => {
        const newSet = new Set(prev);
        newSet.delete(bundleId);
        return newSet;
      });
      // Pass the bundle companies with a special flag to indicate removal
      onSelectBundle(bundle.companies, currentRole, `REMOVE:${bundle.title}`);
    } else {
      // Add bundle - add companies to selection
      setSelectedBundles(prev => new Set(prev).add(bundleId));
      onSelectBundle(bundle.companies, currentRole, bundle.title);
    }
  };

  return (
    <section className="w-full">
      {/* Desktop Layout */}
      <div className="hidden md:block">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {availableBundles.map((bundle) => (
            <BundleCard
              key={bundle.title}
              bundle={bundle}
              isSelected={selectedBundles.has(bundle.id || bundle.title)}
              onToggle={() => handleBundleToggle(bundle)}
              currentRole={currentRole}
            />
          ))}
        </div>
      </div>

      {/* Mobile Layout - Clean Swipe Experience */}
      <div className="md:hidden">
        <div className="relative">
          {/* Carousel Container with Enhanced Swipe */}
          <div 
            className="overflow-hidden rounded-lg"
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
          >
            <div 
              className="flex transition-transform duration-300 ease-out"
              style={{ transform: `translateX(-${currentIndex * 100}%)` }}
            >
              {availableBundles.map((bundle) => (
                <div key={bundle.title} className="w-full flex-shrink-0 px-2">
                  <BundleCard
                    bundle={bundle}
                    isSelected={selectedBundles.has(bundle.id || bundle.title)}
                    onToggle={() => handleBundleToggle(bundle)}
                    currentRole={currentRole}
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Clean Progress Indicators */}
          {availableBundles.length > 1 && (
            <div className="mt-4">
              {/* Enhanced Dots with Labels */}
              <div className="flex justify-center items-center gap-2">
                {availableBundles.map((bundle, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentIndex(index)}
                    className={`transition-all duration-200 rounded-full ${
                      index === currentIndex 
                        ? 'w-8 h-2 bg-[#4F46E5]' 
                        : 'w-2 h-2 bg-gray-300 hover:bg-gray-400 active:scale-110'
                    }`}
                    aria-label={`Go to ${bundle.title}`}
                  />
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </section>
  );
}

function BundleCard({ 
  bundle, 
  isSelected,
  onToggle,
  currentRole
}: { 
  bundle: CompanyBundle; 
  isSelected: boolean;
  onToggle: () => void;
  currentRole: string[];
}) {
  const [isExpanded, setIsExpanded] = React.useState(false);
  const [companyData, setCompanyData] = React.useState<Record<string, string>>({});

  // Fetch company data silently without logging
  React.useEffect(() => {
    async function fetchCompanyData() {
      const supabase = createClient()
      const { data } = await supabase
        .from('companies')
        .select('name, logo_url')
        .in('name', bundle.companies)

      if (data) {
        const logoMap = data.reduce((acc, company) => ({
          ...acc,
          [company.name]: company.logo_url
        }), {})
        setCompanyData(logoMap)
      }
    }

    fetchCompanyData()
  }, [bundle.companies]);

  const displayCount = 4; // Show 4 overlapping logos for cleaner look
  const remainingCount = Math.max(0, bundle.companies.length - displayCount);

  return (
    <Card 
      className={`transition-all duration-200 border cursor-pointer ${
        isSelected 
          ? 'ring-2 ring-blue-500 border-blue-300 bg-blue-50/30' 
          : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'
      }`}
      onClick={onToggle}
    >
      <CardContent className="p-3">
        {/* Header - Checkbox, Bundle Title and Company Count */}
        <div className="mb-3">
          <div className="flex items-start gap-3 mb-1">
            {/* Checkbox */}
            <div className={`flex-shrink-0 w-5 h-5 rounded border-2 flex items-center justify-center transition-all duration-200 mt-0.5 ${
              isSelected 
                ? 'bg-blue-600 border-blue-600' 
                : 'border-gray-300 hover:border-blue-400'
            }`}>
              {isSelected && (
                <Check className="w-3 h-3 text-white" />
              )}
            </div>
            
            {/* Title and Count */}
            <div className="flex-1 min-w-0">
              <h3 className="text-sm font-semibold text-gray-900 leading-tight mb-1 truncate">
                {bundle.title}
              </h3>
              <p className="text-xs text-gray-500">
                {bundle.companies.length} companies
              </p>
            </div>
          </div>
        </div>

        {/* Overlapping Company Logos */}
        <div className="mb-3">
          <div className="flex items-center mb-2">
            <div className="flex items-center">
              {bundle.companies.slice(0, displayCount).map((company, index) => (
                <div
                  key={index}
                  className="relative w-7 h-7 rounded-sm overflow-hidden border border-white"
                  style={{ 
                    marginLeft: index > 0 ? '-5px' : '0',
                    zIndex: displayCount - index 
                  }}
                >
                  <CompanyLogo
                    name={company}
                    logoUrl={companyData[company] || null}
                    size={28}
                  />
                </div>
              ))}
              
              {/* Remaining count indicator */}
              {remainingCount > 0 && (
                <div
                  className="relative w-7 h-7 rounded-sm bg-gray-100 flex items-center justify-center text-xs font-medium text-gray-600 border border-white"
                  style={{ 
                    marginLeft: '-5px',
                    zIndex: 0
                  }}
                >
                  +{remainingCount}
                </div>
              )}
            </div>
          </div>

          {/* Expanded Company List */}
          {isExpanded && (
            <div className="mt-2 pt-2 border-t border-gray-200">
              <div className="space-y-1 max-h-32 overflow-y-auto">
                {bundle.companies.map((company) => (
                  <div
                    key={company}
                    className="flex items-center gap-2 p-2 bg-gray-50 rounded-sm"
                  >
                    <div className="w-4 h-4">
                      <CompanyLogo
                        name={company}
                        logoUrl={companyData[company] || null}
                        size={16}
                      />
                    </div>
                    <span className="text-xs text-gray-700 truncate">{company}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* See entire list button */}
        <div>
          <button
            onClick={(e) => {
              e.stopPropagation();
              setIsExpanded(!isExpanded);
            }}
            className="w-full text-xs text-blue-600 hover:text-blue-700 transition-colors py-1 hover:bg-blue-50/50 rounded-sm"
          >
            {isExpanded ? 'Hide' : 'See all'}
          </button>
        </div>
      </CardContent>
    </Card>
  );
}

function AlertCard({ 
  company, 
  role, 
  logoUrl,
  onSelect
}: { 
  company: string; 
  role: string; 
  logoUrl: string;
  onSelect: () => void;
}) {
  return (
    <button 
      onClick={onSelect}
      className="group flex items-center gap-4 p-3 rounded-lg border border-gray-200 hover:border-gray-300 bg-white transition-all hover:shadow-sm w-full"
    >
      <div className="relative w-12 h-12">
        <CompanyLogo
          name={company}
          logoUrl={logoUrl || null}
          size={48}
        />
      </div>
      
      <div className="flex-1 text-left">
        <h3 className="font-medium text-gray-900 group-hover:text-[#4F46E5] transition-colors">
          {company}
        </h3>
        <p className="text-sm text-gray-500">{role}</p>
      </div>

      <Bell className="w-4 h-4 text-gray-400 group-hover:text-[#4F46E5] transition-colors" />
    </button>
  );
} 