'use client'

import { JobRole, roleDisplayNames } from '@/lib/roles';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface JobFiltersProps {
  roleFilter: string[];
  onRoleChange: (newRole: string[]) => void;
  className?: string;
  currentRole?: string[];
}

export function JobFilters({ roleFilter, onRoleChange, className, currentRole }: JobFiltersProps) {
  // Use currentRole if provided, otherwise fallback to roleFilter
  const displayRole = currentRole || roleFilter;
  
  return (
    <Select
      value={displayRole[0]}
      onValueChange={(value) => onRoleChange([value])}
    >
      <SelectTrigger className={className}>
        <SelectValue placeholder="Select a role">
          {displayRole[0] === '*' ? 'All Roles' : roleDisplayNames[displayRole[0] as JobRole]}
        </SelectValue>
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="*">All Roles</SelectItem>
        {Object.entries(JobRole).map(([key, value]) => (
          <SelectItem key={key} value={key}>
            {roleDisplayNames[value as JobRole]}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}