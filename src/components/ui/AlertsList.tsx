'use client'

import { useState, useEffect } from 'react'
import { FiTrash2 } from 'react-icons/fi'
import { ConfirmDialog } from './ConfirmDialog'
import { formatRoleForDisplay } from '@/lib/roles'
import { motion } from 'framer-motion'
import { Bell } from 'lucide-react'
import { CompanyLogo } from './CompanyLogo'
import { createClient } from '@/lib/supabase'

interface AlertsListProps {
  companies: string[]
  role: string[] | null
  onDeleteCompany: (companyToDelete: string) => Promise<void>
}

export function AlertsList({
  companies,
  role,
  onDeleteCompany,
}: AlertsListProps) {
  const [confirmDialog, setConfirmDialog] = useState({
    isOpen: false,
    companyToDelete: ''
  })
  const [companyData, setCompanyData] = useState<Record<string, string>>({})

  useEffect(() => {
    async function fetchCompanyData() {
      if (companies.length === 0) return;
      
      const supabase = createClient()
      const { data } = await supabase
        .from('companies')
        .select('name, logo_url')
        .in('name', companies)

      if (data) {
        const logoMap = data.reduce((acc, company) => ({
          ...acc,
          [company.name]: company.logo_url
        }), {})
        setCompanyData(logoMap)
      }
    }

    fetchCompanyData()
  }, [companies])

  const handleDeleteClick = (company: string, e: React.MouseEvent) => {
    e.stopPropagation()
    setConfirmDialog({
      isOpen: true,
      companyToDelete: company
    })
  }

  if (companies.length === 0) {
    return (
      <div className="bg-white rounded-xl border border-gray-200 px-4 sm:px-6 py-10 text-center">
        <div className="max-w-sm mx-auto space-y-5">
          <div className="flex justify-center">
            <div className="w-12 h-12 rounded-full bg-[#4F46E5]/5 flex items-center justify-center">
              <Bell className="w-6 h-6 text-[#4F46E5]" />
            </div>
          </div>
          <div className="space-y-2">
            <h3 className="text-lg font-medium text-gray-900">
              No alerts yet
            </h3>
            <p className="text-sm text-gray-600">
              Create your first alert in seconds and be first to apply when roles open up
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {companies.map((company) => (
          <motion.div
            key={company}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="group bg-white rounded-xl border border-gray-200 p-4 hover:border-gray-300 transition-all hover:shadow-sm"
          >
            <div className="flex items-start gap-4">
              <div className="relative w-12 h-12 rounded-lg overflow-hidden flex-shrink-0 bg-gray-50">
                <CompanyLogo
                  name={company}
                  logoUrl={companyData[company] || null}
                  size={48}
                />
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between gap-2">
                  <div>
                    <h3 className="font-medium text-gray-900 truncate mb-1">
                      {company}
                    </h3>
                    <p className="text-sm text-gray-500">
                      {Array.isArray(role) && role[0] === '*' 
                        ? 'All roles' 
                        : formatRoleForDisplay(Array.isArray(role) ? role[0] : role)}
                    </p>
                  </div>
                  <button
                    onClick={(e) => handleDeleteClick(company, e)}
                    className="p-2 text-gray-400 hover:text-red-500 transition-colors flex-shrink-0"
                    aria-label={`Remove ${company}`}
                  >
                    <FiTrash2 className="text-lg" />
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      <ConfirmDialog
        isOpen={confirmDialog.isOpen}
        onClose={() => setConfirmDialog({ isOpen: false, companyToDelete: '' })}
        onConfirm={() => {
          onDeleteCompany(confirmDialog.companyToDelete)
          setConfirmDialog({ isOpen: false, companyToDelete: '' })
        }}
        title="Delete Alert"
        message={`Are you sure you want to delete the alert for ${confirmDialog.companyToDelete}?`}
      />
    </div>
  )
} 