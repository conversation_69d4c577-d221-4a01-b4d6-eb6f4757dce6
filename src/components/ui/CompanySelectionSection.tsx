'use client';

import * as React from "react"
import { JobRole, roleDisplayNames } from '@/lib/roles'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { PopularAlertCards } from './PopularAlertCards'
import { JobSearchSection } from './JobSearchSection'
import { Card, CardContent } from "./card"
import { Search, ChevronDown, ChevronUp, X, Bell, MapPin, ExternalLink } from 'lucide-react'
import { createClient } from '@/lib/supabase'
import { CompanyLogo } from './CompanyLogo'
import { useFilter } from '@/contexts/FilterContext'
import toast from 'react-hot-toast'
import Link from 'next/link'
import { formatDistanceToNow } from 'date-fns'
import { formatRoleForDisplay } from '@/lib/roles'
import { CompanySearch } from './CompanySearch'
import { Button } from './button'
import { JobFilters } from './JobFilters'
import { AlertProgressBar } from './AlertProgressBar'
import { useAuth } from '@/contexts/AuthContext'
import { Input } from './input'
import { UnifiedJobCard } from './UnifiedJobCard'

interface Company {
  name: string;
  logo_url: string | null;
}

interface JobListing {
  id: number;
  title: string;
  company_name: string;
  category_dept: string;
  date_posted: string;
  years_of_experience: number | null;
  location: string;
  url: string;
  companies?: {
    id: number;
    name: string;
    logo_url: string;
    careers_page_url?: string;
  } | null;
}

interface CompanySelectionSectionProps {
  currentRole: string[];
  onRoleChange: (newRole: string[]) => Promise<void>;
  onSelectBundle: (companies: string[], role: string[], bundleTitle?: string) => void;
  onAlertCreated: (data: { companies: string[]; role: string[]; error?: 'already_tracking' | 'update_failed' }) => void;
  addedBundles?: string[];
  initialCompanies?: string[];
  isAuthenticated?: boolean;
  onSignupClick?: (companies: string[], role: string[]) => void;
}

// Helper function to format locations
const formatLocations = (location: string | null | undefined): string => {
  if (!location || location.trim() === '') return 'Not available';
  
  try {
    const locations = JSON.parse(location);
    if (Array.isArray(locations)) {
      const validLocations = locations.filter(loc => loc && loc.trim() !== '');
      return validLocations.length > 0 ? validLocations.join(' • ') : 'Not available';
    }
    return location.trim() || 'Not available';
  } catch {
    return location.trim() || 'Not available';
  }
};

// Job Preview Component
function JobPreview({ 
  selectedCompanies, 
  currentRole,
  logoCache = {},
  onFetchLogos
}: { 
  selectedCompanies: string[]; 
  currentRole: string[]; 
  logoCache?: Record<string, string | null>;
  onFetchLogos?: (companies: string[]) => Promise<Record<string, string | null>>;
}) {
  const [jobs, setJobs] = React.useState<JobListing[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [companyLogos, setCompanyLogos] = React.useState<Record<string, string | null>>({});
  const [currentPage, setCurrentPage] = React.useState(1);
  const [totalJobs, setTotalJobs] = React.useState(0);
  const jobsPerPage = 10;

  // Create cache keys to prevent unnecessary re-fetching
  const companiesKey = React.useMemo(() => 
    selectedCompanies.sort().join(','), [selectedCompanies]
  );
  const roleKey = React.useMemo(() => currentRole[0], [currentRole]);
  const cacheKey = React.useMemo(() => 
    `${companiesKey}-${roleKey}`, [companiesKey, roleKey]
  );
  
  // Cache for job data and logos to prevent flickering
  const [dataCache, setDataCache] = React.useState<Record<string, {
    jobs: JobListing[];
    totalJobs: number;
    logos: Record<string, string | null>;
    timestamp: number;
  }>>({});

  // Check if we have cached data that's still fresh (within 5 minutes)
  const getCachedData = React.useCallback((key: string) => {
    const cached = dataCache[key];
    if (!cached) return null;
    
    const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);
    if (cached.timestamp < fiveMinutesAgo) return null;
    
    return cached;
  }, [dataCache]);

  // Use shared logo cache and fetch missing logos if needed
  React.useEffect(() => {
    if (selectedCompanies.length === 0) return;

    // Check if we have cached logos first
    const cached = getCachedData(cacheKey);
    if (cached && Object.keys(cached.logos).length > 0) {
      setCompanyLogos(cached.logos);
      return;
    }
    
    // Use shared cache or fetch missing logos
    const fetchLogos = async () => {
      if (onFetchLogos) {
        const logos = await onFetchLogos(selectedCompanies);
        setCompanyLogos(logos);
      } else {
        // Fallback: use provided cache
        const result: Record<string, string | null> = {};
        selectedCompanies.forEach(company => {
          result[company] = logoCache[company] || null;
        });
        setCompanyLogos(result);
      }
    };

    fetchLogos();
  }, [selectedCompanies, cacheKey, getCachedData, logoCache, onFetchLogos]);

  React.useEffect(() => {
    if (selectedCompanies.length === 0) {
      setJobs([]);
      setTotalJobs(0);
      setCurrentPage(1);
      return;
    }

    // Check if we have cached data first
    const cached = getCachedData(cacheKey);
    if (cached) {
      setJobs(cached.jobs);
      setTotalJobs(cached.totalJobs);
      setCompanyLogos(cached.logos);
      setCurrentPage(1);
      // Don't set loading to false here since we're using cached data
      return;
    }

    const fetchJobs = async () => {
      setLoading(true);
      try {
        const supabase = createClient();
        
        // First, get the total count for pagination
        let countQuery = supabase
          .from('job_listings')
          .select('*', { count: 'exact', head: true })
          .in('company_name', selectedCompanies);

        // Filter by role if not "all roles"
        if (currentRole[0] !== '*') {
          countQuery = countQuery.eq('category_dept', currentRole[0]);
        }

        const { count, error: countError } = await countQuery;

        if (countError) {
          console.error('Error getting job count:', countError);
          setTotalJobs(0);
        } else {
          setTotalJobs(count || 0);
        }

        // Then fetch the jobs for current page
        const offset = (currentPage - 1) * jobsPerPage;
        
        let query = supabase
          .from('job_listings')
          .select(`
            id,
            title,
            company_name,
            category_dept,
            date_posted,
            years_of_experience,
            location,
            url
          `)
          .in('company_name', selectedCompanies)
          .order('date_posted', { ascending: false, nullsFirst: false })
          .range(offset, offset + jobsPerPage - 1);

        // Filter by role if not "all roles"
        if (currentRole[0] !== '*') {
          query = query.eq('category_dept', currentRole[0]);
        }

        const { data, error } = await query;

        if (error) {
          console.error('Error fetching jobs:', error);
          setJobs([]);
        } else {
          const mappedJobs: JobListing[] = (data || []).map(job => ({
            id: job.id,
            title: job.title,
            company_name: job.company_name,
            category_dept: job.category_dept,
            date_posted: job.date_posted,
            years_of_experience: job.years_of_experience,
            location: job.location,
            url: job.url,
            companies: null // We don't need this since we fetch logos separately
          }));
          
          setJobs(mappedJobs);
          
          // Cache the results
          setDataCache(prev => ({
            ...prev,
            [cacheKey]: {
              jobs: mappedJobs,
              totalJobs: count || 0,
              logos: companyLogos,
              timestamp: Date.now()
            }
          }));
        }
      } catch (error) {
        console.error('Error fetching jobs:', error);
        setJobs([]);
        setTotalJobs(0);
      } finally {
        setLoading(false);
      }
    };

    // Reset to page 1 when companies or role changes
    setCurrentPage(1);
    
    // Debounce the fetch to avoid too many API calls
    const timeoutId = setTimeout(fetchJobs, 300);
    return () => clearTimeout(timeoutId);
  }, [selectedCompanies, currentRole, cacheKey, getCachedData, companyLogos]);

  // Fetch jobs when page changes (only if not cached)
  React.useEffect(() => {
    if (selectedCompanies.length === 0) return;
    if (currentPage === 1) return; // Already handled by the main effect above

    const fetchJobsForPage = async () => {
      setLoading(true);
      try {
        const supabase = createClient();
        const offset = (currentPage - 1) * jobsPerPage;
        
        let query = supabase
          .from('job_listings')
          .select(`
            id,
            title,
            company_name,
            category_dept,
            date_posted,
            years_of_experience,
            location,
            url
          `)
          .in('company_name', selectedCompanies)
          .order('date_posted', { ascending: false, nullsFirst: false })
          .range(offset, offset + jobsPerPage - 1);

        // Filter by role if not "all roles"
        if (currentRole[0] !== '*') {
          query = query.eq('category_dept', currentRole[0]);
        }

        const { data, error } = await query;

        if (error) {
          console.error('Error fetching jobs for page:', error);
          setJobs([]);
        } else {
          const mappedJobs: JobListing[] = (data || []).map(job => ({
            id: job.id,
            title: job.title,
            company_name: job.company_name,
            category_dept: job.category_dept,
            date_posted: job.date_posted,
            years_of_experience: job.years_of_experience,
            location: job.location,
            url: job.url,
            companies: null
          }));
          setJobs(mappedJobs);
        }
      } catch (error) {
        console.error('Error fetching jobs for page:', error);
        setJobs([]);
      } finally {
        setLoading(false);
      }
    };

    fetchJobsForPage();
  }, [currentPage, selectedCompanies, currentRole]);

  if (selectedCompanies.length === 0) return null;

  const totalPages = Math.ceil(totalJobs / jobsPerPage);
  const startJob = (currentPage - 1) * jobsPerPage + 1;
  const endJob = Math.min(currentPage * jobsPerPage, totalJobs);

  return (
    <div className="bg-gradient-to-br from-indigo-50 to-purple-50 border border-indigo-200 rounded-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="text-sm font-medium text-indigo-600">
          {totalJobs} jobs found
        </div>
        {totalPages > 1 && (
          <div className="text-sm text-gray-500">
            Showing {startJob}-{endJob} of {totalJobs}
          </div>
        )}
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500"></div>
          <span className="ml-3 text-gray-600">Finding jobs...</span>
        </div>
      ) : jobs.length > 0 ? (
        <div className="space-y-4">
          <div className="space-y-3">
            {jobs.map((job) => {
              // Calculate time posted for display
              const timePosted = job.date_posted ? (() => {
                try {
                  const date = new Date(job.date_posted);
                  if (isNaN(date.getTime())) return undefined;
                  return formatDistanceToNow(date, { addSuffix: true });
                } catch {
                  return undefined;
                }
              })() : undefined;
              
              return (
                <UnifiedJobCard
                  key={job.id}
                  job={{
                    id: job.id,
                    title: job.title,
                    company_name: job.company_name,
                    location: job.location,
                    location_type: null,
                    url: job.url,
                    date_posted: job.date_posted,
                    category_dept: job.category_dept,
                    description: null,
                    years_of_experience: job.years_of_experience,
                    companies: { logo_url: companyLogos[job.company_name] || null }
                  }}
                  context="marketing"
                  isAuthenticated={false}
                  isProUser={false}
                  timePosted={timePosted}
                  showAIFeatures={false}
                  isLimited={false}
                  className="hover:border-indigo-300"
                />
              );
            })}
          </div>

          {/* Pagination Controls */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between pt-4 border-t border-indigo-200">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className={`px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                  currentPage === 1
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-white text-indigo-600 border border-indigo-200 hover:bg-indigo-50'
                }`}
              >
                Previous
              </button>

              <div className="flex items-center gap-2">
                {/* Show page numbers */}
                {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }

                  return (
                    <button
                      key={pageNum}
                      onClick={() => setCurrentPage(pageNum)}
                      className={`w-8 h-8 text-sm font-medium rounded transition-colors ${
                        currentPage === pageNum
                          ? 'bg-indigo-600 text-white'
                          : 'bg-white text-indigo-600 border border-indigo-200 hover:bg-indigo-50'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}
              </div>

              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className={`px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                  currentPage === totalPages
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-white text-indigo-600 border border-indigo-200 hover:bg-indigo-50'
                }`}
              >
                Next
              </button>
            </div>
          )}
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">
          <div className="mb-2">
            <div className="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mx-auto mb-3">
              <Search className="w-6 h-6 text-gray-400" />
            </div>
            <p className="font-medium">No jobs found</p>
            <p className="text-sm mt-1">
              Try selecting different companies or roles
            </p>
          </div>
        </div>
      )}
    </div>
  );
}

// Unified Selection Confirmation Component
function SelectionConfirmation({
  selectedCompanies,
  currentRole,
  onCreateAlert,
  onRemoveCompany,
  isAuthenticated,
  onSignupClick,
  logoCache = {}
}: {
  selectedCompanies: string[];
  currentRole: string[];
  onCreateAlert: () => void;
  onRemoveCompany: (company: string) => void;
  isAuthenticated: boolean;
  onSignupClick?: () => void;
  logoCache?: Record<string, string | null>;
}) {
  // Use the shared logo cache instead of fetching independently
  const companyLogos = React.useMemo(() => {
    const result: Record<string, string | null> = {};
    selectedCompanies.forEach(company => {
      result[company] = logoCache[company] || null;
    });
    return result;
  }, [selectedCompanies, logoCache]);

  if (selectedCompanies.length === 0) return null;

  // Helper function to format role for display
  const getDisplayRole = (roleValue: string) => {
    if (roleValue === '*') return 'All Roles';
    if (roleValue in JobRole) {
      return roleDisplayNames[roleValue as JobRole];
    }
    return roleValue;
  };

  return (
    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mt-6">
      <div className="space-y-4">
        {/* Selection Summary */}
        <div className="flex items-center gap-3 flex-wrap">
          <span className="text-gray-600">Track</span>
          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-700">
            {getDisplayRole(currentRole[0])}
          </span>
          <span className="text-gray-600">roles at</span>
          <div className="flex flex-wrap items-center gap-2">
            {selectedCompanies.map((company) => (
              <div 
                key={company}
                className="inline-flex items-center gap-2 px-3 py-2 bg-white border border-gray-200 rounded-full text-sm shadow-sm hover:shadow-md transition-shadow"
              >
                <div className="w-5 h-5 rounded-sm overflow-hidden flex-shrink-0 bg-gray-50">
                  <CompanyLogo
                    name={company}
                    logoUrl={companyLogos[company] || null}
                    size={20}
                  />
                </div>
                <span className="text-gray-700 font-medium">{company}</span>
                <button
                  onClick={() => onRemoveCompany(company)}
                  className="text-gray-400 hover:text-gray-600 transition-colors ml-1"
                >
                  <X className="w-3.5 h-3.5" />
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* Action Button */}
        <button
          onClick={isAuthenticated ? onCreateAlert : onSignupClick}
          className="w-full px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white font-medium rounded-lg transition-colors flex items-center justify-center gap-2"
        >
          <Bell className="w-4 h-4" />
          {isAuthenticated ? 'Create Alert' : 'Sign up to get notified'}
        </button>
      </div>
    </div>
  );
}

export function CompanySelectionSection({
  currentRole,
  onRoleChange,
  onSelectBundle,
  onAlertCreated,
  addedBundles = [],
  initialCompanies = [], // Keep prop but don't use it for bottom confirmation
  isAuthenticated = false,
  onSignupClick
}: CompanySelectionSectionProps) {
  const [showSearch, setShowSearch] = React.useState(false);
  const [showCompanies, setShowCompanies] = React.useState(false);
  const [allCompanies, setAllCompanies] = React.useState<Company[]>([]);
  const [loadingCompanies, setLoadingCompanies] = React.useState(true);
  
  // Shared logo cache to prevent flickering across all components
  const [globalLogoCache, setGlobalLogoCache] = React.useState<Record<string, string | null>>({});
  
  // Use FilterContext for current session selections only
  const { selectedCompanies, setSelectedCompanies } = useFilter();

  // Fetch company logos and cache them globally
  const fetchAndCacheLogos = React.useCallback(async (companies: string[]) => {
    if (companies.length === 0) return {};
    
    // Check if we already have all logos cached
    const missingLogos = companies.filter(company => !(company in globalLogoCache));
    if (missingLogos.length === 0) {
      // Return cached logos for requested companies
      const result: Record<string, string | null> = {};
      companies.forEach(company => {
        result[company] = globalLogoCache[company];
      });
      return result;
    }
    
    try {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('companies')
        .select('name, logo_url')
        .in('name', missingLogos);

      if (error) {
        console.error('Error fetching company logos:', error);
        return globalLogoCache;
      }

      const newLogos: Record<string, string | null> = {};
      data?.forEach(company => {
        newLogos[company.name] = company.logo_url;
      });
      
      // Update global cache
      setGlobalLogoCache(prev => ({ ...prev, ...newLogos }));
      
      // Return combined result
      const result: Record<string, string | null> = {};
      companies.forEach(company => {
        result[company] = newLogos[company] || globalLogoCache[company] || null;
      });
      return result;
    } catch (error) {
      console.error('Error fetching company logos:', error);
      return globalLogoCache;
    }
  }, [globalLogoCache]);

  // Don't initialize with existing tracked companies - keep it clean for current session only
  // Removed the useEffect that was setting initialCompanies

  // Fetch all companies on component mount
  React.useEffect(() => {
    async function fetchAllCompanies() {
      try {
        const supabase = createClient();
        const { data, error } = await supabase
          .from('companies')
          .select('name, logo_url')
          .order('name');

        if (error) {
          console.error('Error fetching companies:', error);
          return;
        }

        setAllCompanies(data || []);
        
        // Cache all company logos while we're at it
        const logoMap: Record<string, string | null> = {};
        data?.forEach(company => {
          logoMap[company.name] = company.logo_url;
        });
        setGlobalLogoCache(logoMap);
      } catch (error) {
        console.error('Error fetching companies:', error);
      } finally {
        setLoadingCompanies(false);
      }
    }

    fetchAllCompanies();
  }, []);

  // Handle role changes - this affects all selections
  const handleGlobalRoleChange = (newRole: string) => {
    onRoleChange([newRole]);
  };

  // Handle adding/removing companies from bundles
  const handleBundleSelect = (companies: string[], _localRole: string[], bundleTitle?: string) => {
    if (bundleTitle?.startsWith('REMOVE:')) {
      // Bundle was unchecked - remove these specific companies from selection
      const actualBundleTitle = bundleTitle.replace('REMOVE:', '');
      setSelectedCompanies(prev => prev.filter(company => !companies.includes(company)));
      return;
    }
    
    // Add bundle companies to current session selection
    const newCompanies = companies.filter(company => !selectedCompanies.includes(company));
    if (newCompanies.length > 0) {
      setSelectedCompanies(prev => [...prev, ...newCompanies]);
    }
  };

  // Handle company pill click from browse section
  const handleCompanyClick = (companyName: string) => {
    if (currentRole.length === 0) {
      toast.error('Please select a role first');
      return;
    }
    
    if (selectedCompanies.includes(companyName)) {
      // Remove company if already selected (toggle behavior)
      setSelectedCompanies(prev => prev.filter(company => company !== companyName));
      return;
    }
    
    // Add company to current session selection
    setSelectedCompanies(prev => [...prev, companyName]);
  };

  // Handle removing companies from current session selection
  const handleRemoveCompany = (companyName: string) => {
    setSelectedCompanies(prev => prev.filter(company => company !== companyName));
  };

  // Handle final alert creation
  const handleCreateAlert = async () => {
    if (selectedCompanies.length === 0) {
      toast.error('Please select at least one company');
      return;
    }

    if (currentRole.length === 0) {
      toast.error('Please select a role');
      return;
    }

    // Use existing logic to create alert (this will handle duplicate checking)
    onSelectBundle(selectedCompanies, currentRole);
    
    // Clear current session selection after creating alert
    setSelectedCompanies([]);
  };

  // Handle signup redirect for non-authenticated users
  const handleSignupClick = () => {
    if (selectedCompanies.length === 0) {
      toast.error('Please select at least one company');
      return;
    }

    if (currentRole.length === 0) {
      toast.error('Please select a role');
      return;
    }

    if (onSignupClick) {
      onSignupClick(selectedCompanies, currentRole);
    } else {
      toast.success('Please sign up to create alerts');
    }
  };

  return (
    <div className="space-y-4"> {/* Removed conditional padding since no longer using fixed positioning */}
      {/* Compact Top Control Bar - Role Dropdown + Search Field */}
      <div className="bg-gradient-to-r from-purple-50 to-indigo-100 border border-purple-200 rounded-lg p-4 shadow-sm">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Role Selector */}
          <div className="flex-shrink-0">
            <Select
              value={currentRole[0]}
              onValueChange={handleGlobalRoleChange}
            >
              <SelectTrigger className="w-full sm:w-[180px] h-10 border-purple-300 bg-white hover:border-purple-400 focus:border-purple-500 focus:ring-purple-200 text-sm shadow-sm">
                <SelectValue placeholder="Select role">
                  {currentRole[0] === '*' ? 'All Roles' : roleDisplayNames[currentRole[0] as JobRole]}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="*">All Roles</SelectItem>
                {Object.entries(JobRole).map(([key, value]) => (
                  <SelectItem key={key} value={key}>
                    {roleDisplayNames[value]}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Embedded Search Section */}
          <div className="flex-1">
            <JobSearchSection 
              isAuthenticated={isAuthenticated}
              initialCompanies={[]} // Always empty - no pre-population
              initialRole={currentRole}
              onRoleChange={onRoleChange}
              jobs={[]}
              compact={true}
            />
          </div>
        </div>
      </div>

      {/* Bundle Cards */}
      <div>
        <h3 className="text-sm font-medium text-gray-700 mb-3">Quick Add Companies</h3>
        <PopularAlertCards 
          onSelectBundle={handleBundleSelect}
          currentRole={currentRole}
          addedBundles={addedBundles}
        />
      </div>

      {/* Browse All Companies */}
      <div>
        <button
          onClick={() => setShowCompanies(!showCompanies)}
          className="w-full flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-md hover:bg-gray-100 transition-colors text-left"
        >
          <span className="text-sm font-medium text-gray-700">
            Browse All Companies {!loadingCompanies && `(${allCompanies.length})`}
          </span>
          {showCompanies ? (
            <ChevronUp className="w-4 h-4 text-gray-500" />
          ) : (
            <ChevronDown className="w-4 h-4 text-gray-500" />
          )}
        </button>
        
        {/* Expandable Horizontal Scroll Companies */}
        <div 
          className={`overflow-hidden transition-all duration-300 ease-in-out ${
            showCompanies ? 'max-h-[400px] opacity-100 mt-3' : 'max-h-0 opacity-0'
          }`}
        >
          <div className="bg-white border border-gray-200 rounded-md overflow-hidden">
            {loadingCompanies ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-gray-500 text-sm">Loading...</div>
              </div>
            ) : (
              <div className="relative">
                {/* Hint text for scrolling */}
                <div className="px-4 pt-3 pb-1">
                  <span className="text-xs text-gray-500">
                    Scroll horizontally to see more companies →
                  </span>
                </div>
                
                {/* Horizontal scroll container */}
                <div 
                  className="overflow-x-auto scrollbar-hide pb-2"
                  style={{
                    scrollSnapType: 'x mandatory',
                    WebkitOverflowScrolling: 'touch'
                  }}
                >
                  <div 
                    className="inline-flex gap-3 p-4 pt-2"
                    style={{
                      width: `${Math.ceil(allCompanies.length / 3) * 140}px`, // Dynamic width based on company count
                      minWidth: 'calc(100% + 40px)' // Ensure content extends beyond container for bleeding effect
                    }}
                  >
                    {/* Group companies into columns of 3 */}
                    {Array.from({ length: Math.ceil(allCompanies.length / 3) }, (_, columnIndex) => (
                      <div 
                        key={columnIndex}
                        className="flex flex-col gap-2 flex-shrink-0"
                        style={{ 
                          width: '130px',
                          scrollSnapAlign: 'start'
                        }}
                      >
                        {allCompanies.slice(columnIndex * 3, (columnIndex + 1) * 3).map((company) => (
                          <button
                            key={company.name}
                            onClick={() => handleCompanyClick(company.name)}
                            className={`flex items-center gap-2 p-2 border rounded-md transition-all duration-200 group text-left w-full ${
                              selectedCompanies.includes(company.name)
                                ? 'bg-blue-100 border-blue-300 text-blue-700'
                                : 'bg-white border-gray-200 hover:border-blue-300 hover:bg-blue-50/50'
                            }`}
                          >
                            <div className="w-5 h-5 rounded-sm overflow-hidden flex-shrink-0 bg-gray-50">
                              <CompanyLogo
                                name={company.name}
                                logoUrl={company.logo_url}
                                size={20}
                              />
                            </div>
                            <span className={`text-xs font-medium truncate ${
                              selectedCompanies.includes(company.name)
                                ? 'text-blue-700'
                                : 'text-gray-700 group-hover:text-blue-700'
                            }`}>
                              {company.name}
                            </span>
                          </button>
                        ))}
                      </div>
                    ))}
                  </div>
                </div>
                
                {/* Enhanced fade effect on right edge to indicate more content */}
                <div className="absolute top-8 right-0 bottom-2 w-12 bg-gradient-to-l from-white via-white/90 to-transparent pointer-events-none" />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Unified Selection Confirmation - Show when companies are selected */}
      {selectedCompanies.length > 0 && (
        <SelectionConfirmation
          selectedCompanies={selectedCompanies}
          currentRole={currentRole}
          onCreateAlert={handleCreateAlert}
          onRemoveCompany={handleRemoveCompany}
          isAuthenticated={isAuthenticated}
          onSignupClick={handleSignupClick}
          logoCache={globalLogoCache}
        />
      )}

      {/* Job Preview Section - Show jobs AFTER the signup button as proof of value */}
      {selectedCompanies.length > 0 && (
        <JobPreview 
          selectedCompanies={selectedCompanies}
          currentRole={currentRole}
          logoCache={globalLogoCache}
          onFetchLogos={fetchAndCacheLogos}
        />
      )}
    </div>
  );
} 