'use client'

import React, { createContext, useContext, useEffect, useState, useMemo } from 'react'
import { Session, User } from '@supabase/supabase-js'
import { createClient } from '@/lib/supabase'
import { useRouter } from 'next/navigation'

type AuthContextType = {
  user: User | null
  session: Session | null
  loading: boolean
  signOut: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    const supabase = createClient()
    let mounted = true
    
    const checkSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession()
        if (error) throw error
        
        if (mounted) {
          setSession(session)
          setUser(session?.user ?? null)
        }
      } catch (error) {
        console.error('Error checking session:', error)
        setSession(null)
        setUser(null)
      } finally {
        if (mounted) setLoading(false)
      }
    }

    // Handle auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (!mounted) return
        
        setSession(session)
        setUser(session?.user ?? null)
        setLoading(false)

        switch (event) {
          case 'SIGNED_IN':
            // Only redirect to dashboard if coming from auth pages
            const currentPath = window.location.pathname
            if (currentPath === '/signin' || currentPath === '/signup' || currentPath === '/auth/callback') {
              router.push('/dashboard')
            }
            break
          case 'SIGNED_OUT':
            // Remove the redirect here since it's handled in signOut function
            break
          case 'TOKEN_REFRESHED':
            // Update session without navigation
            setSession(session)
            break
        }
      }
    )

    checkSession()

    return () => {
      mounted = false
      subscription.unsubscribe()
    }
  }, [router])

  // Sign out function
  const signOut = async () => {
    const supabase = createClient()
    setLoading(true)
    
    try {
      // First set local state to null to prevent auto-login
      setUser(null)
      setSession(null)
      
      // Then sign out from Supabase
      await supabase.auth.signOut()
      
      // Use replace instead of push to prevent back-button from returning to protected pages
      router.replace('/signin')
    } catch (error) {
      console.error('Sign out error:', error)
      router.replace('/signin')
    } finally {
      setLoading(false)
    }
  }

  const value = useMemo(
    () => ({
      user,
      session,
      loading,
      signOut,
    }),
    [user, session, loading, signOut]
  )

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
