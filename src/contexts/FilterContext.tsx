// src/contexts/FilterContext.tsx

/**
 * This file defines a React context for managing filter states in a job search application.
 * It provides a centralized way to store and update filter values for companies, roles,
 * experience levels, locations, and alert frequencies. The FilterProvider component allows child components
 * to access and modify these filter states, while the useFilter hook provides an easy way
 * for components to consume this context.
 */

import React, { createContext, useState, useContext, useMemo } from 'react';
import { JobRole, roleDisplayNames } from '@/lib/roles';

// Define the shape of our context
interface FilterContextType {
  selectedCompanies: string[];
  setSelectedCompanies: React.Dispatch<React.SetStateAction<string[]>>;
}

// Create the context with undefined as the default value
const FilterContext = createContext<FilterContextType | undefined>(undefined);

// FilterProvider component to wrap around parts of the app that need access to the filter state
export const FilterProvider = ({ 
  children,
  initialCompanies = [],
}: { 
  children: React.ReactNode;
  initialCompanies?: string[];
}) => {
  const [selectedCompanies, setSelectedCompanies] = useState<string[]>(initialCompanies);

  // Memoize the context value to prevent unnecessary re-renders
  const value = useMemo(() => ({
    selectedCompanies,
    setSelectedCompanies,
  }), [selectedCompanies]);

  return (
    <FilterContext.Provider value={value}>
      {children}
    </FilterContext.Provider>
  );
};

// Custom hook to use the filter context in components
export const useFilter = () => {
  const context = useContext(FilterContext);
  if (context === undefined) {
    // Ensure the hook is used within a FilterProvider
    throw new Error('useFilter must be used within a FilterProvider');
  }
  return context;
};
