import React, { createContext, useContext, useEffect, useState, useMemo } from 'react'
import { createClient } from '@/lib/supabase'
import { useAuth } from './AuthContext'

export interface UserProfile {
  id: string
  plan: string
  subscription_end_date: string | null
  filter_companies: string[]
  filter_role: string[] | null
  filter_experience: string | null
  filter_location: string | null
  filter_alert_frequency: string | null
  added_bundles?: string[]
}

interface UserProfileContextType {
  profile: UserProfile | null
  loading: boolean
  isProUser: boolean
  refreshProfile: () => Promise<void>
  updateProfile: (updates: Partial<UserProfile>) => Promise<void>
}

const UserProfileContext = createContext<UserProfileContextType | undefined>(undefined)

export function UserProfileProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth()
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)

  // Memoized value for isProUser
  const isProUser = useMemo(() => {
    if (!profile) return false
    return (
      profile.plan === 'pro' &&
      (!profile.subscription_end_date || new Date(profile.subscription_end_date) > new Date())
    )
  }, [profile])

  // Function to refresh profile data
  const refreshProfile = async () => {
    if (!user) {
      setProfile(null)
      setLoading(false)
      return
    }

    try {
      const supabase = createClient()
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (error) throw error
      setProfile(data)
    } catch (error) {
      console.error('Error fetching user profile:', error)
      setProfile(null)
    } finally {
      setLoading(false)
    }
  }

  // Function to update profile data
  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!user || !profile) return

    try {
      const supabase = createClient()
      const { error } = await supabase
        .from('user_profiles')
        .update(updates)
        .eq('id', user.id)

      if (error) throw error
      
      // Update local state with new values
      setProfile({ ...profile, ...updates })
    } catch (error) {
      console.error('Error updating user profile:', error)
      throw error
    }
  }

  // Initial profile fetch
  useEffect(() => {
    refreshProfile()
  }, [user])

  const value = useMemo(
    () => ({
      profile,
      loading,
      isProUser,
      refreshProfile,
      updateProfile,
    }),
    [profile, loading, isProUser]
  )

  return <UserProfileContext.Provider value={value}>{children}</UserProfileContext.Provider>
}

export function useUserProfile() {
  const context = useContext(UserProfileContext)
  if (context === undefined) {
    throw new Error('useUserProfile must be used within a UserProfileProvider')
  }
  return context
} 