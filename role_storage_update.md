# Alert Storage System Update

We need to modify how alerts are stored to support different roles for different companies. Currently, we have separate columns for companies and roles, which prevents users from having different role alerts for different companies.

## Database Update
Convert from separate columns to a JSONB structure:

Current:
- filter_companies (text[])
- filter_role (text)

New:
- job_alerts (JSONB)
  Structure: {
    "alerts": [
      {
        "company": "string",
        "role": "string",
        "created_at": "timestamp"
      }
    ]
  }

Migration SQL:
1. Add new column
2. Migrate existing data
3. Add index
4. Remove old columns

## Frontend Changes
1. Update UserProfile interface
2. Modify alert creation in JobSearchSection
3. Update AlertsTable to display company-role pairs
4. Adjust FilterContext for new structure
5. Update Dashboard component alert handling

## Backend Changes
1. Update profile fetching queries
2. Modify alert management functions
3. Adjust job matching logic
4. Update email generation

## Testing Requirements
1. Verify alert creation/deletion
2. Test job matching with new structure
3. Validate email notifications
4. Check migration process

## Performance Considerations
1. Monitor JSONB query performance
2. Consider pagination for large alert lists
3. Implement caching if needed

Let me know if you'd like specific code implementations for any of these changes.
