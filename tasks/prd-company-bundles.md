# Product Requirements Document: Enhanced Company Selection Bundles

## Introduction/Overview

The Enhanced Company Selection Bundles feature builds upon the existing `PopularAlertCards` component to provide an improved curated company selection experience. Currently, users can select from "Fastest Growing AI Companies" and "Fastest Growing SaaS Companies" bundles in a carousel format. This enhancement will expand the bundle system with additional categories ("Top Remote Companies"), improve the mobile experience (critical for our 55% mobile user base), and provide better visual presentation with side-by-side bundle cards instead of the current carousel approach.

This feature solves the onboarding friction where users struggle to find relevant companies and often abandon the setup process due to search limitations and overwhelming individual selection.

## Goals

1. **Reduce Setup Time**: Decrease user onboarding time from company selection by 60%
2. **Improve Search Success**: Eliminate failed company searches by providing curated options
3. **Increase User Engagement**: Boost the average number of companies tracked per user
4. **Enhance Mobile Experience**: Optimize for the 55% of users on mobile devices
5. **Support Multiple Use Cases**: Enable both new user onboarding and existing user expansion

## User Stories

**As a new user signing up for Awaloon,**
I want to quickly select groups of companies that match my interests,
So that I can start receiving job alerts without spending time searching for individual companies.

**As an existing user wanting to expand my job search,**
I want to browse curated company bundles by category,
So that I can discover new companies I might not have considered.

**As a user interested in AI companies,**
I want to see a "Top AI Startups" bundle with company logos,
So that I can quickly identify and select companies I want to track.

**As a user who values work-life balance,**
I want to select a "Top Remote Companies" bundle,
So that I can focus my job search on companies with flexible work arrangements.

**As a user exploring multiple industries,**
I want to select multiple bundles simultaneously,
So that I can cast a wider net for job opportunities.

## Functional Requirements

### Enhanced Bundle System (Building on Existing)
1. **Bundle Display**: The system must display 3 company bundles side-by-side on desktop, stacked on mobile
2. **Bundle Categories**: The system must support these bundle types:
   - "Fastest Growing AI Companies" (existing - expand from current 7 companies)
   - "Fastest Growing SaaS Companies" (existing - expand from current 7 companies)  
   - "Top Remote Companies" (new - companies with strong remote-first culture)
3. **Visual Design**: Each bundle card must prominently display:
   - Bundle name and description
   - Company logos in a visually appealing grid/stack
   - Company names
4. **Data Structure**: Extend existing `COMPANY_BUNDLES` array and `CompanyBundle` interface

### Mobile-First User Experience
5. **Mobile Layout**: On mobile devices, bundles must stack vertically with full-width cards
6. **Touch Interactions**: All interactions must be optimized for touch (larger tap targets, swipe gestures)
7. **Performance**: Bundle cards must load quickly on mobile networks
8. **Responsive Company Logos**: Company logo grids must adapt to different screen sizes

### Enhanced User Interaction Flow (Improving Existing)
9. **Bundle Selection**: Users must be able to click on any bundle card to initiate selection
10. **Improved Preview**: When a bundle is selected, enhance the existing expanded view:
    - Larger, more prominent company removal buttons for mobile
    - Better visual hierarchy for company list
    - Clearer confirmation flow
11. **Multiple Selection**: Users must be able to select multiple bundles in a single session
12. **Company Addition**: Upon confirmation, selected companies must be added to the user's tracking list

### Role Integration
13. **Role-First Flow**: The system must integrate with the existing role selection (role as parent category)
14. **Role Filtering**: Bundle selection should work within the context of previously selected roles

### Dashboard Integration
15. **Placement**: The bundle section must be appended to the bottom of the current dashboard
16. **Responsive Design**: Bundle cards must be responsive and work on mobile devices
17. **Existing Flow**: The feature must coexist with the current JobSearchSection component

## Non-Goals (Out of Scope)

1. **Dynamic Bundle Creation**: Users cannot create their own custom bundles (admin-only via JSON)
2. **Real-time Job Counts**: Bundle cards will not display current job opening numbers
3. **Trending Indicators**: No "trending" or "popular" badges on bundles
4. **Bundle Analytics**: No tracking of bundle performance or selection rates in v1
5. **Signup Page Integration**: Initial implementation focuses on existing users only

## Design Considerations

### Visual Style
- Follow existing Awaloon design system with indigo/purple color scheme
- Use card-based layout similar to "What You Get with Awaloon" section
- Implement hover effects and smooth transitions consistent with current UI
- Company logos should use the existing CompanyLogo component

### Layout Structure
```
[Role Selection - Existing]
[Current JobSearchSection - Existing]
[NEW: Company Bundles Section]
  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
  │ Top AI      │ │ Top SaaS    │ │ Top Remote  │
  │ Startups    │ │ Startups    │ │ Companies   │
  │ [logos...]  │ │ [logos...]  │ │ [logos...]  │
  └─────────────┘ └─────────────┘ └─────────────┘
```

### User Flow
1. User selects role (existing flow)
2. User scrolls to bundles section
3. User clicks on desired bundle(s)
4. System shows confirmation modal with company list
5. User can optionally remove individual companies from the bundle
6. User confirms final selection
7. Companies are added to tracking list
8. User proceeds to complete setup

## Technical Considerations

### Refactoring Existing Components
- Enhance existing `PopularAlertCards.tsx` component instead of creating new one
- Extend existing `CompanyBundle` interface and `COMPANY_BUNDLES` data
- Improve existing `BundleCard` component for better mobile experience
- Maintain existing integration with dashboard and user profile system

### Mobile-First Responsive Design
- Use CSS Grid/Flexbox for responsive bundle layout
- Implement touch-friendly interaction patterns
- Optimize image loading for mobile networks
- Test across different mobile device sizes

### Data Structure Enhancement
- Extend existing `COMPANY_BUNDLES` array with new "Top Remote Companies" bundle
- Add optional fields to `CompanyBundle` interface (description, category, etc.)
- Maintain backward compatibility with existing dashboard integration

## Success Metrics

### Primary Metrics
1. **Mobile Conversion Rate**: Increase mobile bundle selection by 40%
2. **Setup Completion Rate**: Increase from baseline by 25%
3. **Average Companies Selected**: Increase from current average by 40%
4. **Mobile Performance**: Bundle cards load in <2 seconds on 3G networks

### Secondary Metrics
1. **Bundle Selection Rate**: Track which bundles are most popular by device type
2. **Multiple Bundle Usage**: Percentage of users selecting 2+ bundles (mobile vs desktop)
3. **User Retention**: Track if bundle users have higher 7-day retention by device type

## Open Questions

1. **Bundle Size**: Current bundles have 7 companies each. Should we maintain this or expand to 8-12?
2. **Mobile Layout**: Should mobile show one bundle at a time (carousel) or stack all three?
3. **Company Overlap**: Current bundles don't overlap. Should "Top Remote Companies" include companies from other bundles?
4. **Performance**: Should we implement lazy loading for company logos on mobile?
5. **Existing Data**: Should we migrate existing `added_bundles` tracking or enhance it?

---

**Target Implementation Timeline**: 2-3 development cycles
**Priority Level**: High - Core user experience improvement, mobile-critical
**Dependencies**: Existing PopularAlertCards component, CompanyBundle interface, dashboard integration 