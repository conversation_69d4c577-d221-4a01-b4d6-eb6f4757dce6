# PRD: Company Hiring Insights Card

## Introduction/Overview

Replace the current job table display in the alert creation flow with an intelligent hiring insights card that provides market intelligence instead of overwhelming users with raw job data. This feature transforms how users understand hiring activity across their selected companies and roles, making the value proposition clearer before they create job alerts.

**Problem:** The current job table shows "X jobs found" with a paginated list that can be overwhelming and doesn't provide actionable insights about hiring trends or market activity.

**Solution:** A single, scannable insights card that aggregates job data into meaningful hiring intelligence while maintaining the same alert creation functionality.

## Goals

1. **Reduce cognitive load** - Replace overwhelming job tables with digestible hiring insights
2. **Increase alert conversion** - Provide clear value proposition before asking users to create alerts
3. **Maintain functionality** - Keep all existing alert creation logic and job query capabilities
4. **Improve user experience** - Deliver market intelligence instead of raw job listings
5. **Support scalability** - Handle edge cases from 0 jobs to 1000+ jobs across multiple companies

## User Stories

1. **As a job seeker**, I want to see hiring trends across my target companies so that I can understand market activity before creating an alert.

2. **As a user selecting multiple companies**, I want to see which companies are most actively hiring in my role so that I can prioritize my job search efforts.

3. **As a user with no current job matches**, I want clear messaging about setting up an alert so that I'll be notified when relevant opportunities become available.

4. **As a mobile user**, I want the insights to be easily readable on my device so that I can quickly assess hiring activity on the go.

5. **As a user tracking a single company**, I want specific insights about that company's hiring patterns so that I can understand their recruitment activity.

## Functional Requirements

1. **Data Aggregation**
   - 1.1 System must use existing job query results that currently populate the job table
   - 1.2 System must group jobs by company name
   - 1.3 System must count jobs posted in the last 14 days (filtered by posting date)
   - 1.4 System must count total active jobs matching the query
   - 1.5 System must sort companies by job count in descending order

2. **Display Logic**
   - 2.1 System must show top 3 companies by job count when multiple companies are selected
   - 2.2 System must display "+X more companies" when more than 3 companies have jobs
   - 2.3 System must list all companies when only 1-3 companies total have jobs
   - 2.4 System must handle proper pluralization (1 role vs 2 roles, 1 company vs 2 companies)

3. **Card Structure**
   - 3.1 Card must display: "📊 Hiring insights for [ROLE] roles:"
   - 3.2 Card must show: "X roles posted in last 2 weeks across Y companies"
   - 3.3 Card must show: "Z total active roles"
   - 3.4 Card must display: "Top activity:" section with company breakdown
   - 3.5 Card must include the existing purple "Create Alert" button at the bottom

4. **Edge Case Handling**
   - 4.1 When no jobs found: Display "No active [ROLE] roles found across selected companies. Set an alert and we'll notify you when opportunities become available."
   - 4.2 When no recent jobs (last 2 weeks): Display "0 roles posted in last 2 weeks • Z total active roles"
   - 4.3 When single company selected: Display "X roles posted in last 2 weeks at [Company Name]" with company-specific insights
   - 4.4 When large datasets (1000+ jobs): Display summary with "top 5 companies that have posted the most jobs in this category"

5. **Integration Requirements**
   - 5.1 System must completely replace the existing job table component
   - 5.2 System must position after company selection chips
   - 5.3 System must maintain all existing alert creation functionality
   - 5.4 System must use same purple button styling as current implementation
   - 5.5 System must preserve responsive design for mobile devices

## Non-Goals (Out of Scope)

1. **Backend Changes** - No modifications to job fetching APIs or database queries
2. **Real-time Updates** - No live data refresh or "updated X minutes ago" indicators
3. **Job Detail Views** - No ability to view individual job listings from the insights card
4. **Advanced Analytics** - No historical trend charts or complex data visualizations
5. **Company Profiles** - No clickable company names leading to company detail pages
6. **Alert Modification** - No changes to existing alert creation, editing, or management flows

## Design Considerations

1. **Visual Hierarchy**
   - Use existing card styling from dashboard components
   - Employ 📊 emoji or similar chart icon for visual appeal
   - Maintain clear typography hierarchy with role and metrics prominence

2. **Responsive Design**
   - Stack information vertically on mobile devices
   - Ensure touch-friendly button sizing
   - Maintain readability across all screen sizes

3. **Content Layout**
   - Keep information scannable with clear sections
   - Use bullet points and consistent formatting
   - Ensure CTA button is prominent and accessible

## Technical Considerations

1. **Data Dependencies**
   - `job.company_name` - for grouping and company identification
   - `job.posting_date` - for recent activity calculation (last 14 days)
   - `job.is_active` - for total active job count
   - Selected role from UI state
   - Selected companies from UI state

2. **Component Architecture**
   - Replace job table component entirely with new insights card component
   - Maintain existing job query and filtering logic
   - Preserve alert creation button functionality and styling

3. **Performance Considerations**
   - Leverage existing job data without additional API calls
   - Implement efficient client-side aggregation and sorting
   - Ensure fast rendering for large datasets

## Success Metrics

1. **User Engagement**
   - Increase in alert creation conversion rate from the insights card
   - Reduced time spent on job selection page before alert creation
   - Decreased bounce rate from job selection to alert creation

2. **User Experience**
   - Positive user feedback on clarity and usefulness of insights
   - Reduced support tickets related to understanding job availability
   - Improved mobile user engagement with alert creation

3. **Technical Performance**
   - Maintain current page load times
   - Handle edge cases gracefully without errors
   - Preserve existing alert creation success rates

## Open Questions

1. **Large Dataset Handling** - For 1000+ jobs across many companies, should we show "top 5 companies" or a different threshold?
2. **Date Range Flexibility** - Should the "last 2 weeks" timeframe be configurable or remain fixed?
3. **Company Activity Details** - For single company selection, what additional insights would be most valuable?
4. **Analytics Integration** - What specific user interaction events should be tracked for product insights?
5. **Accessibility** - Are there specific WCAG compliance requirements for the insights card?
