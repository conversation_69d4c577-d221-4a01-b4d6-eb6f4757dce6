# PRD: Dashboard Tracked Jobs Feature

## 1. Overview

### 1.1 Product Vision
Add a new section to the main dashboard that displays recent job listings from companies the user is currently tracking. This feature will provide immediate value by showing relevant opportunities without requiring users to navigate to separate pages.

### 1.2 Problem Statement
Currently, users must:
- Navigate to the Job Hunt page to see matched jobs
- Manually search through company listings to find new opportunities
- Switch between different sections to understand what jobs are available from their tracked companies

### 1.3 Success Metrics
- Increased user engagement on dashboard page
- Reduced time to discover relevant job opportunities
- Higher click-through rates to job applications
- Improved user retention and session duration

## 2. User Stories

### 2.1 Primary User Stories
**As a job seeker**, I want to see recent jobs from companies I'm tracking on my dashboard, so I can quickly identify new opportunities without navigating to multiple pages.

**As a job seeker**, I want to see the most recent jobs first, so I can apply to opportunities as soon as they become available.

**As a job seeker**, I want to see job details in a familiar format, so I can quickly assess if a position is relevant to me.

### 2.2 Secondary User Stories
**As a job seeker**, I want to access AI insights and key contacts for tracked jobs, so I can improve my application strategy.

**As a job seeker**, I want to see when jobs were posted, so I can prioritize my applications.

## 3. Functional Requirements

### 3.1 Core Features

#### 3.1.1 Tracked Jobs Display
- Display jobs from companies in user's `filter_companies` array
- Show maximum 10 jobs per page with pagination
- Sort by `date_posted` DESC (newest first)
- Use existing `UnifiedJobCard` component for consistency

#### 3.1.2 Data Integration
- Fetch from `job_listings` table where `company_name` matches tracked companies
- Include company logo from `companies` table via join
- Filter to jobs posted within last 30 days by default

#### 3.1.3 User Interface
- Add new section to dashboard below existing company selection
- Section title: "Recent Jobs from Your Tracked Companies"
- Show loading states during data fetch
- Display empty state when no tracked companies or no recent jobs
- Maintain responsive design for mobile and desktop

### 3.2 Technical Requirements

#### 3.2.1 Data Fetching
- Create `useTrackedJobs` hook for data management
- Implement proper error handling and loading states
- Use Supabase `createClient()` for database queries
- Implement pagination with 10 jobs per page

#### 3.2.2 Performance
- Debounce API calls when user updates tracked companies
- Cache results to prevent unnecessary re-fetching
- Implement proper cleanup for React hooks

#### 3.2.3 Integration Points
- Integrate with existing `UserProfileContext` for tracked companies
- Use existing `UnifiedJobCard` component
- Maintain compatibility with existing dashboard layout

## 4. Technical Specifications

### 4.1 Database Schema
**Existing Tables Used:**
- `user_profiles.filter_companies` (string[]) - List of tracked companies
- `job_listings` - Job data with company_name, date_posted, etc.
- `companies` - Company logos and metadata

### 4.2 API Endpoints
**Supabase Query:**
```sql
SELECT job_listings.*, companies.logo_url
FROM job_listings
LEFT JOIN companies ON job_listings.company_name = companies.name
WHERE job_listings.company_name = ANY($1)
AND job_listings.date_posted >= $2
ORDER BY job_listings.date_posted DESC
LIMIT 10 OFFSET $3
```

### 4.3 Component Architecture
```
Dashboard Page
├── DashboardLayout
├── CompanySelectionSection (existing)
├── TrackedJobsSection (new)
│   ├── useTrackedJobs hook
│   ├── UnifiedJobCard (existing)
│   └── Pagination controls
└── Other existing sections
```

## 5. User Experience Design

### 5.1 Layout Integration
- Position below existing company selection section
- Use consistent spacing and styling with existing dashboard
- Maintain visual hierarchy with clear section separation

### 5.2 Loading States
- Skeleton loading for job cards during initial fetch
- Spinner for pagination loading
- Graceful error states with retry options

### 5.3 Empty States
- No tracked companies: "Start tracking companies to see job opportunities here"
- No recent jobs: "No recent jobs from your tracked companies. Check back soon!"
- Error state: "Unable to load jobs. Please try again."

## 6. Implementation Phases

### Phase 1: Core Implementation
1. Create `useTrackedJobs` hook
2. Create `TrackedJobsSection` component
3. Integrate into dashboard page
4. Basic styling and responsive design

### Phase 2: Enhanced Features
1. Add pagination controls
2. Implement loading and error states
3. Add empty state handling
4. Performance optimizations

### Phase 3: Advanced Features
1. AI insights integration for pro users
2. Job filtering within tracked jobs
3. "Mark as seen" functionality
4. Export/share capabilities

## 7. Acceptance Criteria

### 7.1 Functional Criteria
- [ ] Jobs display from user's tracked companies only
- [ ] Jobs sorted by date posted (newest first)
- [ ] Maximum 10 jobs per page with pagination
- [ ] Responsive design works on mobile and desktop
- [ ] Loading states display during data fetching
- [ ] Empty states show appropriate messaging
- [ ] Error handling with user-friendly messages

### 7.2 Technical Criteria
- [ ] Uses existing `UnifiedJobCard` component
- [ ] Integrates with `UserProfileContext`
- [ ] Implements proper React cleanup
- [ ] Follows existing code patterns and conventions
- [ ] No performance regressions on dashboard load

### 7.3 Quality Criteria
- [ ] Consistent visual design with existing dashboard
- [ ] Accessible markup and keyboard navigation
- [ ] Proper TypeScript types and interfaces
- [ ] Error boundaries prevent crashes
- [ ] Graceful degradation for edge cases

## 8. Dependencies and Risks

### 8.1 Dependencies
- Existing `UnifiedJobCard` component
- `UserProfileContext` for tracked companies
- Supabase database access
- Dashboard layout structure

### 8.2 Risks and Mitigations
**Risk:** Performance impact on dashboard load
**Mitigation:** Implement lazy loading and pagination

**Risk:** Empty states for new users
**Mitigation:** Clear onboarding flow to add tracked companies

**Risk:** Stale job data
**Mitigation:** Implement refresh mechanisms and cache invalidation

## 9. Future Enhancements

### 9.1 Potential Features
- Real-time job notifications
- Advanced filtering within tracked jobs
- Job recommendation engine
- Integration with external job boards
- Bulk actions for job management

### 9.2 Analytics and Monitoring
- Track click-through rates on tracked jobs
- Monitor section engagement metrics
- A/B test different layouts and presentations
- User feedback collection on feature usefulness
