# Task List: Company Hiring Insights Card

## Relevant Files

- `components/JobAlerts/HiringInsightsCard.tsx` - Main insights card component replacing job table
- `components/JobAlerts/HiringInsightsCard.test.tsx` - Unit tests for insights card component
- `components/JobAlerts/JobAlertsPage.tsx` - Parent component containing job alert creation flow
- `components/JobAlerts/JobAlertsPage.test.tsx` - Updated tests for job alerts page
- `lib/utils/jobInsights.ts` - Utility functions for job data aggregation and insights calculation
- `lib/utils/jobInsights.test.ts` - Unit tests for job insights utilities
- `types/jobInsights.ts` - TypeScript interfaces for insights data structures

### Notes

- Unit tests should be placed alongside the code files they are testing
- Use `npx jest [optional/path/to/test/file]` to run tests
- Running without a path executes all tests found by the Jest configuration

## Tasks

- [ ] 1.0 Research and analyze current job table implementation
- [ ] 2.0 Create job insights utility functions and data structures
- [ ] 3.0 Build the HiringInsightsCard component
- [ ] 4.0 Integrate insights card into job alerts flow
- [ ] 5.0 Handle edge cases and responsive design
- [ ] 6.0 Testing and quality assurance
