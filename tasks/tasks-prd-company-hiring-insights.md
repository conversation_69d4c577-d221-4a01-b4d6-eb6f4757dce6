# Task List: Company Hiring Insights Card

## Relevant Files

- `components/JobAlerts/HiringInsightsCard.tsx` - Main insights card component replacing job table
- `components/JobAlerts/HiringInsightsCard.test.tsx` - Unit tests for insights card component
- `components/Dashboard/JobAlertsSection.tsx` - Dashboard job alerts component (authenticated users)
- `components/Dashboard/JobAlertsSection.test.tsx` - Tests for dashboard job alerts section
- `components/Marketing/JobAlertsFlow.tsx` - Marketing page job alerts component (unauthenticated users)
- `components/Marketing/JobAlertsFlow.test.tsx` - Tests for marketing job alerts flow
- `lib/utils/jobInsights.ts` - Utility functions for job data aggregation and insights calculation
- `lib/utils/jobInsights.test.ts` - Unit tests for job insights utilities
- `types/jobInsights.ts` - TypeScript interfaces for insights data structures

### Notes

- Unit tests should be placed alongside the code files they are testing
- Use `npx jest [optional/path/to/test/file]` to run tests
- Running without a path executes all tests found by the Jest configuration

## Tasks

- [ ] 1.0 Research and analyze current job table implementations (both dashboard and marketing)
  - [x] 1.1 Locate and examine dashboard job alerts component and its job table implementation
  - [x] 1.2 Locate and examine marketing page job alerts component and its job table implementation
  - [x] 1.3 Identify shared components, utilities, and data structures used by both implementations
  - [x] 1.4 Document current job query logic, data flow, and alert creation flows for both experiences
  - [x] 1.5 Identify authentication state handling and sign-up flow integration points
  - [x] 1.6 Map out current styling, responsive behavior, and component hierarchy

- [/] 2.0 Create job insights utility functions and data structures
  - [x] 2.1 Create TypeScript interfaces for job insights data structures (InsightsData, CompanyActivity, etc.)
  - [ ] 2.2 Implement job aggregation function to group jobs by company
  - [ ] 2.3 Implement recent activity calculation (jobs posted in last 14 days)
  - [ ] 2.4 Implement total active jobs counting function
  - [ ] 2.5 Implement company sorting logic (by job count, descending)
  - [ ] 2.6 Create formatting utilities for pluralization and display text
  - [ ] 2.7 Write comprehensive unit tests for all utility functions

- [ ] 3.0 Build the HiringInsightsCard component
  - [ ] 3.1 Create base HiringInsightsCard component with proper TypeScript props interface
  - [ ] 3.2 Implement main insights display (role, recent activity, total jobs)
  - [ ] 3.3 Implement top companies activity section with proper formatting
  - [ ] 3.4 Add chart emoji/icon and maintain existing card styling patterns
  - [ ] 3.5 Implement responsive design for mobile (vertical stacking)
  - [ ] 3.6 Add Create Alert button with proper styling and click handling
  - [ ] 3.7 Write unit tests for component rendering and user interactions

- [ ] 4.0 Integrate insights card into dashboard job alerts flow (authenticated users)
  - [ ] 4.1 Identify exact location of job table in dashboard component
  - [ ] 4.2 Replace job table with HiringInsightsCard component
  - [ ] 4.3 Pass job data and user selections to insights card
  - [ ] 4.4 Connect Create Alert button to existing authenticated alert creation flow
  - [ ] 4.5 Ensure proper error handling and loading states
  - [ ] 4.6 Update component tests to reflect new insights card integration

- [ ] 5.0 Integrate insights card into marketing job alerts flow (unauthenticated users)
  - [ ] 5.1 Identify exact location of job table in marketing component
  - [ ] 5.2 Replace job table with HiringInsightsCard component
  - [ ] 5.3 Pass job data and user selections to insights card
  - [ ] 5.4 Connect Create Alert button to existing sign-up flow (preserve context)
  - [ ] 5.5 Ensure user selections (companies, role) are maintained through authentication
  - [ ] 5.6 Update component tests to reflect new insights card integration

- [ ] 6.0 Handle edge cases and responsive design across both experiences
  - [ ] 6.1 Implement "no jobs found" messaging with appropriate CTAs
  - [ ] 6.2 Handle "no recent jobs" scenario (0 jobs in last 2 weeks)
  - [ ] 6.3 Implement single company selection special formatting
  - [ ] 6.4 Handle large datasets (1000+ jobs) with "top 5 companies" display
  - [ ] 6.5 Test responsive behavior on mobile devices for both dashboard and marketing
  - [ ] 6.6 Verify accessibility compliance (WCAG guidelines)

- [ ] 7.0 Testing and quality assurance for both user flows
  - [ ] 7.1 Run full test suite and ensure all existing tests pass
  - [ ] 7.2 Test authenticated user flow: company selection → insights → alert creation
  - [ ] 7.3 Test unauthenticated user flow: company selection → insights → sign-up → alert creation
  - [ ] 7.4 Test all edge cases (no jobs, single company, large datasets) on both flows
  - [ ] 7.5 Perform cross-browser testing (Chrome, Firefox, Safari, Edge)
  - [ ] 7.6 Test mobile responsiveness on various device sizes
  - [ ] 7.7 Verify no regression in existing alert creation, authentication, or sign-up functionality
