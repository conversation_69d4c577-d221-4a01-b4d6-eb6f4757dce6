# Task Breakdown: Dashboard Tracked Jobs Feature

## Phase 1: Core Implementation

### 1.1 Data Layer Implementation

#### 1.1.1 Create useTrackedJobs Hook
**File:** `src/hooks/useTrackedJobs.ts`
**Estimated Time:** 2-3 hours
**Dependencies:** None

**Requirements:**
- Fetch jobs from user's tracked companies using Supabase
- Implement pagination (10 jobs per page)
- Sort by date_posted DESC (newest first)
- Handle loading, error, and empty states
- Return interface with jobs, loading, error, pagination controls

**Interface:**
```typescript
interface UseTrackedJobsReturn {
  jobs: TrackedJob[]
  loading: boolean
  error: string | null
  currentPage: number
  totalPages: number
  hasNextPage: boolean
  hasPreviousPage: boolean
  nextPage: () => void
  previousPage: () => void
  refresh: () => void
}
```

**Acceptance Criteria:**
- [ ] Hook fetches jobs from tracked companies only
- [ ] Implements proper pagination with 10 jobs per page
- [ ] Sorts jobs by date_posted DESC
- [ ] Handles loading states correctly
- [ ] Provides error handling with user-friendly messages
- [ ] Includes proper TypeScript types
- [ ] Follows existing hook patterns in codebase

#### 1.1.2 Define TypeScript Interfaces
**File:** `src/hooks/useTrackedJobs.ts` (within same file)
**Estimated Time:** 30 minutes
**Dependencies:** 1.1.1

**Requirements:**
- Define TrackedJob interface based on existing job_listings schema
- Ensure compatibility with UnifiedJobCard component
- Include company logo data from join

**Interface:**
```typescript
interface TrackedJob {
  id: number
  title: string
  company_name: string
  location?: string | null
  location_type?: string | null
  url?: string | null
  date_posted?: string | null
  category_dept?: string | null
  description?: string | null
  years_of_experience?: number | null
  companies?: {
    logo_url: string | null
  } | null
}
```

### 1.2 Component Implementation

#### 1.2.1 Create TrackedJobsSection Component
**File:** `src/components/ui/TrackedJobsSection.tsx`
**Estimated Time:** 3-4 hours
**Dependencies:** 1.1.1, 1.1.2

**Requirements:**
- Use useTrackedJobs hook for data
- Render list of UnifiedJobCard components
- Handle loading, error, and empty states
- Implement responsive design
- Add section header and description

**Component Structure:**
```typescript
interface TrackedJobsSectionProps {
  className?: string
}

export function TrackedJobsSection({ className }: TrackedJobsSectionProps)
```

**Acceptance Criteria:**
- [ ] Uses useTrackedJobs hook for data management
- [ ] Renders UnifiedJobCard for each job
- [ ] Shows loading skeleton during data fetch
- [ ] Displays appropriate empty states
- [ ] Handles errors gracefully with retry option
- [ ] Responsive design works on mobile and desktop
- [ ] Follows existing component patterns

#### 1.2.2 Implement Loading States
**File:** `src/components/ui/TrackedJobsSection.tsx` (within same file)
**Estimated Time:** 1 hour
**Dependencies:** 1.2.1

**Requirements:**
- Skeleton loading for job cards
- Loading spinner for pagination
- Smooth transitions between states

**Acceptance Criteria:**
- [ ] Skeleton cards match UnifiedJobCard dimensions
- [ ] Loading states are visually consistent with existing patterns
- [ ] Smooth transitions prevent layout shifts

#### 1.2.3 Implement Empty States
**File:** `src/components/ui/TrackedJobsSection.tsx` (within same file)
**Estimated Time:** 1 hour
**Dependencies:** 1.2.1

**Requirements:**
- No tracked companies state
- No recent jobs state
- Error state with retry button

**Empty State Messages:**
- No tracked companies: "Start tracking companies to see job opportunities here"
- No recent jobs: "No recent jobs from your tracked companies. Check back soon!"
- Error state: "Unable to load jobs. Please try again."

**Acceptance Criteria:**
- [ ] Clear messaging for each empty state
- [ ] Actionable CTAs where appropriate
- [ ] Consistent styling with existing empty states

### 1.3 Integration

#### 1.3.1 Integrate into Dashboard Page
**File:** `src/app/dashboard/page.tsx`
**Estimated Time:** 1 hour
**Dependencies:** 1.2.1

**Requirements:**
- Add TrackedJobsSection below CompanySelectionSection
- Maintain existing dashboard layout and spacing
- Ensure proper responsive behavior

**Acceptance Criteria:**
- [ ] Section appears in correct position on dashboard
- [ ] Maintains existing dashboard layout
- [ ] No visual regressions in other dashboard sections
- [ ] Responsive design works correctly

#### 1.3.2 Update Dashboard Layout Styling
**File:** `src/app/dashboard/page.tsx`
**Estimated Time:** 30 minutes
**Dependencies:** 1.3.1

**Requirements:**
- Add consistent spacing between sections
- Ensure visual hierarchy is maintained
- Match existing dashboard styling patterns

**Acceptance Criteria:**
- [ ] Consistent spacing with other dashboard sections
- [ ] Visual hierarchy is clear and logical
- [ ] Styling matches existing dashboard patterns

## Phase 2: Enhanced Features

### 2.1 Pagination Implementation

#### 2.1.1 Add Pagination Controls
**File:** `src/components/ui/TrackedJobsSection.tsx`
**Estimated Time:** 2 hours
**Dependencies:** 1.2.1

**Requirements:**
- Previous/Next buttons
- Page number display
- Disable buttons when appropriate
- Loading states during pagination

**Acceptance Criteria:**
- [ ] Pagination controls are intuitive and accessible
- [ ] Buttons disable appropriately at boundaries
- [ ] Loading states during page changes
- [ ] Keyboard navigation support

#### 2.1.2 Implement Pagination Logic
**File:** `src/hooks/useTrackedJobs.ts`
**Estimated Time:** 1 hour
**Dependencies:** 1.1.1, 2.1.1

**Requirements:**
- Track current page state
- Calculate total pages
- Implement next/previous functions
- Handle edge cases

**Acceptance Criteria:**
- [ ] Pagination logic handles edge cases correctly
- [ ] State updates trigger appropriate re-renders
- [ ] No unnecessary API calls during pagination

### 2.2 Performance Optimization

#### 2.2.1 Implement Data Caching
**File:** `src/hooks/useTrackedJobs.ts`
**Estimated Time:** 2 hours
**Dependencies:** 1.1.1

**Requirements:**
- Cache fetched pages to prevent re-fetching
- Implement cache invalidation strategy
- Optimize for common navigation patterns

**Acceptance Criteria:**
- [ ] Previously fetched pages load instantly
- [ ] Cache invalidates appropriately when data changes
- [ ] Memory usage remains reasonable

#### 2.2.2 Add Debounced Refresh
**File:** `src/hooks/useTrackedJobs.ts`
**Estimated Time:** 1 hour
**Dependencies:** 2.2.1

**Requirements:**
- Debounce refresh calls when tracked companies change
- Prevent excessive API calls
- Maintain responsive user experience

**Acceptance Criteria:**
- [ ] Rapid changes to tracked companies don't cause API spam
- [ ] User experience remains responsive
- [ ] Final state is always accurate

## Phase 3: Advanced Features (Future)

### 3.1 AI Integration

#### 3.1.1 Add AI Insights Support
**File:** `src/components/ui/TrackedJobsSection.tsx`
**Estimated Time:** 3 hours
**Dependencies:** 1.2.1, existing AI components

**Requirements:**
- Pass AI feature props to UnifiedJobCard
- Handle pro user restrictions
- Integrate with existing rate limiting

#### 3.1.2 Add Key Contacts Support
**File:** `src/components/ui/TrackedJobsSection.tsx`
**Estimated Time:** 2 hours
**Dependencies:** 3.1.1

**Requirements:**
- Enable key contacts feature for tracked jobs
- Maintain consistency with job-hunt page
- Handle pro user restrictions

### 3.2 Advanced Filtering

#### 3.2.1 Add Job Filtering Controls
**File:** `src/components/ui/TrackedJobsSection.tsx`
**Estimated Time:** 4 hours
**Dependencies:** 1.2.1

**Requirements:**
- Filter by role, experience, location
- Maintain filter state across pagination
- Clear filter options

#### 3.2.2 Implement Filter Logic
**File:** `src/hooks/useTrackedJobs.ts`
**Estimated Time:** 3 hours
**Dependencies:** 3.2.1

**Requirements:**
- Apply filters to database queries
- Update pagination when filters change
- Optimize query performance

## Testing and Quality Assurance

### 4.1 Unit Testing
**Files:** `src/hooks/__tests__/useTrackedJobs.test.ts`, `src/components/ui/__tests__/TrackedJobsSection.test.tsx`
**Estimated Time:** 4 hours
**Dependencies:** All implementation tasks

**Requirements:**
- Test hook functionality with mocked data
- Test component rendering and interactions
- Test error handling and edge cases

### 4.2 Integration Testing
**Files:** `src/app/dashboard/__tests__/page.test.tsx`
**Estimated Time:** 2 hours
**Dependencies:** 1.3.1

**Requirements:**
- Test dashboard integration
- Test responsive behavior
- Test user interactions

### 4.3 Manual Testing
**Estimated Time:** 3 hours
**Dependencies:** All implementation tasks

**Requirements:**
- Test across different devices and browsers
- Test with various data scenarios
- Test performance under load

## Total Estimated Time: 35-40 hours

## Priority Order for Implementation:
1. Phase 1: Core Implementation (1.1.1 → 1.1.2 → 1.2.1 → 1.2.2 → 1.2.3 → 1.3.1 → 1.3.2)
2. Phase 2: Enhanced Features (2.1.1 → 2.1.2 → 2.2.1 → 2.2.2)
3. Testing and QA (4.1 → 4.2 → 4.3)
4. Phase 3: Advanced Features (Future implementation)
