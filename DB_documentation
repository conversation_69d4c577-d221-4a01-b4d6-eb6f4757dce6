# Database Documentation

## user_profiles Table

The `user_profiles` table stores additional information about users beyond what is stored in the `auth.users` table.

### Current Structure

- `id` (uuid): Primary key, matches the id in auth.users
- `name` (text): User's full name
- `email` (text): User's email address
- `role` (text): User's role (e.g., job_seeker, employer, recruiter)
- `created_at` (timestamp with time zone): Timestamp of profile creation
- `filter_companies` (ARRAY): Array of company names for job filtering
- `filter_role` (text): Preferred job role for filtering
- `filter_experience` (text): Preferred experience level for filtering
- `filter_location` (text): Preferred location for job filtering
- `is_verified` (boolean): Indicates if the user's email is verified

### Signup Flow and Profile Creation

1. When a user signs up, their basic information is stored in the `auth.users` table by Supabase Auth.
2. A trigger `on_auth_user_created` on the `auth.users` table calls the `create_initial_profile` function.
3. The `create_initial_profile` function creates a new row in `user_profiles` with initial data from `auth.users`.

### create_initial_profile Function

This function dynamically creates a new user profile when a user signs up. It's designed to be resilient to future table changes.

### Best Practices for Modifying user_profiles

1. When adding new columns:
   - Make them nullable (allow NULL values), or
   - Provide a default value
   - If neither is possible, update the `create_initial_profile` function

2. After any schema changes:
   - Review and update the `create_initial_profile` function if necessary
   - Test the signup flow thoroughly

3. Regularly maintain and update this documentation when changes are made

4. Always test the signup process after any changes to the table or related functions

### Potential Issues and Troubleshooting

- If users are unable to sign up, check the following:
  1. Ensure the `on_auth_user_created` trigger is properly set up on `auth.users`
  2. Verify the `create_initial_profile` function is up to date with the current table structure
  3. Check Supabase logs for any error messages during the signup process

- If new columns are not being populated:
  1. Update the `create_initial_profile` function to include logic for the new columns
  2. If the columns should have specific default values, add them to the function

Remember to keep this documentation updated as you make changes to the database structure or related functions.
