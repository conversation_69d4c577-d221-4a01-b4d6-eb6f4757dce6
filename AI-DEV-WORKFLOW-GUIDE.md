# AI Dev Tasks Workflow Guide 🤖

## What You Just Added To Your Project

You now have a structured workflow for building features with AI assistance! Here are the 3 key files:

- **`create-prd.mdc`** - Helps create Product Requirement Documents
- **`generate-tasks.mdc`** - Breaks PRDs into actionable task lists  
- **`process-task-list.mdc`** - Guides step-by-step implementation

## How This Workflow Works (Step by Step)

### 🎯 **Step 1: Create a Product Requirement Document (PRD)**

When you want to build a new feature, start here:

1. **In Cursor's AI Agent Chat, type:**
   ```
   Use @create-prd.mdc
   Here's the feature I want to build: [Describe your feature]
   Reference these files to help you: [Optional: @src/app/page.tsx @components/ui/button.tsx]
   ```

2. **The AI will ask clarifying questions** like:
   - What problem does this solve?
   - Who is the target user?
   - What are the key actions users should be able to perform?
   - Any design requirements?

3. **Answer the questions** - The AI will create a detailed PRD file in your `/tasks` folder

### 📝 **Step 2: Generate Task List from PRD**

Once you have a PRD (like `tasks/prd-user-dashboard.md`):

1. **In Cursor's AI Agent Chat, type:**
   ```
   Take @tasks/prd-user-dashboard.md and create tasks using @generate-tasks.mdc
   ```

2. **The AI will:**
   - Create high-level parent tasks first
   - Ask for your approval
   - Then break them down into detailed sub-tasks
   - Save as `tasks/tasks-prd-user-dashboard.md`

### ⚙️ **Step 3: Process Tasks One by One**

Now the magic happens - systematic implementation:

1. **In Cursor's AI Agent Chat, type:**
   ```
   Please start on task 1.1 and use @process-task-list.mdc
   ```

2. **The AI will:**
   - Work on ONE task at a time
   - Show you the code changes
   - Mark the task as complete
   - Ask for your approval before moving to the next task

3. **You review and approve** by simply saying "yes" or "y"

## 💡 **Example: Let's Build a User Profile Feature**

Here's exactly what you would type:

```
Step 1: Use @create-prd.mdc
Here's the feature I want to build: A user profile page where users can view and edit their personal information including name, email, profile picture, and bio. Users should be able to upload a new profile picture and save changes.
Reference these files: @src/app/page.tsx @components/ui/button.tsx
```

Then follow the questions, and proceed through steps 2 and 3!

## 🎯 **Why This Works So Well**

- **Structure**: No more overwhelming AI with huge requests
- **Control**: You approve each step before moving forward  
- **Quality**: Smaller tasks = better code quality
- **Debugging**: Easy to pinpoint issues when they happen
- **Progress**: Clear visual progress with checkboxes

## 📁 **File Organization**

```
your-project/
├── create-prd.mdc           # Creates PRDs
├── generate-tasks.mdc       # Generates task lists
├── process-task-list.mdc    # Processes tasks step-by-step
├── tasks/                   # All PRDs and task lists go here
│   ├── prd-user-profile.md
│   ├── tasks-prd-user-profile.md
│   └── prd-payment-system.md
└── [your existing code]
```

## 🚀 **Ready To Start?**

Pick any feature you want to build and follow Step 1 above! The AI will guide you through the entire process.

**Pro Tips:**
- Use MAX mode in Cursor for better PRD generation (if budget allows)
- Be specific in your feature descriptions
- Don't hesitate to ask for clarifications during the process
- You can use this for both frontend and backend features

## 🔄 **For Your Backend Project**

Copy these same 3 `.mdc` files to your backend project and follow the same workflow! The process works for any type of development. 