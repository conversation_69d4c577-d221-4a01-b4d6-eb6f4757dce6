# First to Apply Design System

This design system ensures consistency and quality across the First to Apply platform. It combines modern aesthetics with functional design principles to create a premium, user-friendly experience.

## Colors

### Primary Colors
- Primary Black: `#111827`
- Hover Black: `#1f2937`
- Text Black: `#111827`

### Secondary Colors
- Accent Orange: 
  - Background: `bg-orange-50`
  - Text: `text-orange-600`
  - Hover: `bg-orange-100`

### Gray Scale
- Text Primary: `text-gray-900`
- Text Secondary: `text-gray-600`
- Text Muted: `text-gray-400`
- Border: `border-gray-100`
- Background Light: `bg-gray-50`

### Opacity Variants
- Primary with opacity: `#4F46E5/5` (5% opacity)
- White with opacity: `bg-white/90`

## Typography

### Font Family
```css
font-family: Inter, system-ui, -apple-system, sans-serif;
```

### Font Sizes
- Hero: `text-4xl` (36px)
- Heading: `text-3xl` (30px)
- Subheading: `text-xl` (20px)
- Body: `text-base` (16px)
- Small: `text-sm` (14px)

### Font Weights
- Bold: `font-bold` (700)
- Semibold: `font-semibold` (600)
- Medium: `font-medium` (500)
- Regular: `font-normal` (400)

## Spacing

### Padding
- Button: `px-4 py-2`
- Container: `px-6`
- Section: `py-16`
- Input: `px-14 py-4`

### Margins
- Section: `mt-20`
- Component: `mb-12`
- Element: `gap-6`

## Components

### Buttons

#### Primary Button
```css
px-4 py-2 bg-[#4F46E5] hover:bg-[#4338CA] text-white rounded-lg 
transition-all hover:-translate-y-0.5 shadow-sm
```

#### Secondary Button
```css
px-3 py-1.5 bg-gray-50 text-gray-600 rounded-full 
hover:bg-gray-100 transition-all hover:-translate-y-0.5
```

#### Hot Company Pill
```css
bg-gradient-to-r from-orange-50 to-orange-100/50 
text-orange-600 hover:from-orange-100 hover:to-orange-100
```

### Input Fields
```css
px-14 py-4 bg-transparent rounded-xl transition-all 
text-lg focus:outline-none
```

### Cards/Containers
```css
bg-white rounded-2xl p-1 shadow-sm border border-gray-100
```

## Animations

### Transitions
- Default: `transition-all`
- Duration: `duration-300`
- Timing: `ease-in-out`

### Hover Effects
- Scale: `hover:scale-[1.02]`
- Translate: `hover:-translate-y-0.5`
- Opacity: `hover:opacity-90`

## Shadows
- Subtle: `shadow-sm`
- Default: `shadow`
- Focus: `focus:ring-2 focus:ring-[#4F46E5]/10`

## Border Radius
- Default: `rounded-lg` (8px)
- Pill: `rounded-full`
- Container: `rounded-2xl` (16px)

## Layout

### Max Widths
- Container: `max-w-6xl`
- Content: `max-w-4xl`
- Text Block: `max-w-2xl`

### Grid
- Three Column: `grid grid-cols-3 gap-8`
- Two Column: `grid grid-cols-2 gap-12`

### Flexbox
```css
flex items-center justify-between
flex items-center gap-6
```

## Effects

### Backdrop Blur
```css
bg-white/90 backdrop-blur-sm
```

### Gradients
- Background: `bg-gradient-to-b from-gray-50 to-transparent`
- Accent: `bg-gradient-to-r from-orange-50 to-orange-100/50`

## Best Practices

1. **Spacing Consistency**
   - Use predefined spacing values
   - Maintain vertical rhythm with consistent margins
   - Use gap utilities for flexible spacing

2. **Color Usage**
   - Use primary blue for main actions
   - Reserve orange for highlights and important elements
   - Use opacity variants for subtle effects

3. **Typography Hierarchy**
   - Maintain clear size differences between levels
   - Use weight to create emphasis
   - Ensure sufficient contrast with background

4. **Interactive States**
   - Always provide hover feedback
   - Use transitions for smooth interactions
   - Maintain consistent animation timing

5. **Responsive Design**
   - Use relative units when possible
   - Maintain readability at all sizes
   - Test interactive elements on all devices

## Implementation Notes

This design system is implemented using Tailwind CSS. Custom values should be added to the Tailwind config to ensure consistency across the application. When using Cursor or other IDEs, these classes can be referenced for autocompletion and validation.

For custom components or styles not covered by Tailwind, refer to the color codes and values provided in this document to maintain consistency.